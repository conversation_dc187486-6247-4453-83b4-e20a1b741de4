/*
 * Copyright (c) 2022-2023. PortSwigger Ltd. All rights reserved.
 *
 * This code may be used to extend the functionality of Burp Suite Community Edition
 * and Burp Suite Professional, provided that this usage does not violate the
 * license terms for those products.
 */

package burp.api.montoya.burpsuite;

/**
 * Shutdown options that can be used when calling {@link BurpSuite#shutdown(ShutdownOptions...)}.
 */
public enum ShutdownOptions
{
    /**
     * Display a dialog to the user allowing them to confirm or cancel the shutdown
     */
    PROMPT_USER
}
