/*
 * Copyright (c) 2022-2023. PortSwigger Ltd. All rights reserved.
 *
 * This code may be used to extend the functionality of Burp Suite Community Edition
 * and Burp Suite Professional, provided that this usage does not violate the
 * license terms for those products.
 */

package burp.api.montoya.collaborator;

/**
 * SMTP interaction detected by Burp Collaborator.
 */
public interface SmtpDetails
{
    /**
     * SMTP protocol.
     *
     * @return The protocol used by the interaction.
     */
    SmtpProtocol protocol();

    /**
     * SMTP conversation.
     *
     * @return The SMTP conversation between the client and the Collaborator
     * server.
     */
    String conversation();
}
