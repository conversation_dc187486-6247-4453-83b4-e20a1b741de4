/*
 * Copyright (c) 2022-2023. PortSwigger Ltd. All rights reserved.
 *
 * This code may be used to extend the functionality of Burp Suite Community Edition
 * and Burp Suite Professional, provided that this usage does not violate the
 * license terms for those products.
 */

package burp.api.montoya.scanner;

/**
 * This enum represents built in configurations for the Burp Scanner tool.
 */
public enum BuiltInAuditConfiguration
{
    LEGACY_PASSIVE_AUDIT_CHECKS,
    LEGACY_ACTIVE_AUDIT_CHECKS
}
