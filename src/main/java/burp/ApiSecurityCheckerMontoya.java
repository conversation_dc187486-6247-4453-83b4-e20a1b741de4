package burp;

import burp.api.montoya.BurpExtension;
import burp.api.montoya.MontoyaApi;
import burp.api.montoya.extension.ExtensionUnloadingHandler;
import burp.api.montoya.http.handler.HttpHandler;
import burp.api.montoya.http.handler.HttpRequestToBeSent;
import burp.api.montoya.http.handler.HttpResponseReceived;
import burp.api.montoya.http.handler.RequestToBeSentAction;
import burp.api.montoya.http.handler.ResponseReceivedAction;
import burp.api.montoya.http.message.requests.HttpRequest;
import burp.api.montoya.http.message.responses.HttpResponse;
import burp.api.montoya.logging.Logging;
import burp.api.montoya.ui.UserInterface;
import burp.api.montoya.persistence.Persistence;
import burp.api.montoya.persistence.PersistedObject;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.net.URL;

/**
 * API Security Checker - 使用官方Montoya API
 * 保留所有原有功能，迁移到现代化的Montoya API
 */
public class ApiSecurityCheckerMontoya implements BurpExtension, HttpHandler, ExtensionUnloadingHandler {
    
    private MontoyaApi api;
    private Logging logging;
    private UserInterface userInterface;
    private Persistence persistence;
    
    // UI组件 - 简化的现代化界面
    private JPanel mainPanel;
    private JTabbedPane mainTabbedPane;

    // 简化的UI组件
    private JTextArea discoveredEndpointsArea;
    private JTextArea sensitiveInfoArea;
    private JTextArea logArea;
    private JList<String> hostList;

    // 数据模型
    private DefaultListModel<String> hostListModel;
    
    // 数据存储
    private final Map<String, Set<String>> discoveredEndpoints = new ConcurrentHashMap<>();
    private final Set<String> discoveredHosts = ConcurrentHashMap.newKeySet();
    private final List<String> sensitiveInfo = new CopyOnWriteArrayList<>();
    
    // 线程池
    private ExecutorService executorService;
    
    // 配置
    private volatile boolean isEnabled = true;
    private volatile boolean autoScanEnabled = true;
    
    @Override
    public void initialize(MontoyaApi api) {
        this.api = api;
        this.logging = api.logging();
        this.userInterface = api.userInterface();
        this.persistence = api.persistence();
        
        // 设置扩展名称
        api.extension().setName("API Security Checker (Montoya)");
        
        // 初始化组件
        initializeComponents();
        
        // 注册处理器
        registerHandlers();
        
        // 创建UI
        createUserInterface();
        
        // 加载配置
        loadConfiguration();
        
        logging.logToOutput("API Security Checker (Montoya) extension loaded successfully!");
        logging.logToOutput("Version: 2.0 - Using Official Burp Montoya API");
        logging.logToOutput("All original features preserved and enhanced!");
    }
    
    /**
     * 初始化核心组件
     */
    private void initializeComponents() {
        try {
            // 创建线程池
            executorService = Executors.newFixedThreadPool(5);

            // 初始化UI数据模型
            hostListModel = new DefaultListModel<>();

            logging.logToOutput("Core components initialized successfully");

        } catch (Exception e) {
            logging.logToError("Failed to initialize components: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 注册各种处理器
     */
    private void registerHandlers() {
        try {
            // 注册HTTP处理器
            api.http().registerHttpHandler(this);
            
            // 注册扩展卸载处理器
            api.extension().registerUnloadingHandler(this);
            
            logging.logToOutput("Handlers registered successfully");
            
        } catch (Exception e) {
            logging.logToError("Failed to register handlers: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建用户界面 - 现代化的简化界面
     */
    private void createUserInterface() {
        SwingUtilities.invokeLater(() -> {
            try {
                // 创建主面板
                mainPanel = new JPanel(new BorderLayout());
                mainTabbedPane = new JTabbedPane();

                // 创建各个Tab
                createApiDiscoveryTab();
                createSensitiveInfoTab();
                createHostManagementTab();
                createLogTab();
                createConfigurationTab();

                mainPanel.add(mainTabbedPane, BorderLayout.CENTER);

                // 注册Tab到Burp Suite
                userInterface.registerSuiteTab("🔍 API Security Checker (Montoya)", mainPanel);

                logging.logToOutput("Modern UI created successfully using Montoya API");

            } catch (Exception e) {
                logging.logToError("Failed to create user interface: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 创建API发现Tab
     */
    private void createApiDiscoveryTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // 发现的端点显示区域
        discoveredEndpointsArea = new JTextArea(20, 50);
        discoveredEndpointsArea.setEditable(false);
        discoveredEndpointsArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane endpointsScrollPane = new JScrollPane(discoveredEndpointsArea);
        endpointsScrollPane.setBorder(new TitledBorder("发现的API端点"));

        // 控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        JButton clearButton = new JButton("清空");
        JButton exportButton = new JButton("导出");
        JCheckBox autoScanCheckBox = new JCheckBox("自动扫描", autoScanEnabled);

        clearButton.addActionListener(e -> {
            discoveredEndpoints.clear();
            updateEndpointsDisplay();
        });

        exportButton.addActionListener(e -> exportEndpoints());

        autoScanCheckBox.addActionListener(e -> {
            autoScanEnabled = autoScanCheckBox.isSelected();
            logging.logToOutput("Auto scan " + (autoScanEnabled ? "enabled" : "disabled"));
        });

        controlPanel.add(autoScanCheckBox);
        controlPanel.add(clearButton);
        controlPanel.add(exportButton);

        panel.add(controlPanel, BorderLayout.NORTH);
        panel.add(endpointsScrollPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("🔍 API Discovery", panel);
    }

    /**
     * 创建敏感信息Tab
     */
    private void createSensitiveInfoTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // 敏感信息显示区域
        sensitiveInfoArea = new JTextArea(20, 50);
        sensitiveInfoArea.setEditable(false);
        sensitiveInfoArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane sensitiveScrollPane = new JScrollPane(sensitiveInfoArea);
        sensitiveScrollPane.setBorder(new TitledBorder("发现的敏感信息"));

        // 控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        JButton clearButton = new JButton("清空");
        JButton exportButton = new JButton("导出");

        clearButton.addActionListener(e -> {
            sensitiveInfo.clear();
            updateSensitiveInfoDisplay();
        });

        exportButton.addActionListener(e -> exportSensitiveInfo());

        controlPanel.add(clearButton);
        controlPanel.add(exportButton);

        panel.add(controlPanel, BorderLayout.NORTH);
        panel.add(sensitiveScrollPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("🔒 Sensitive Info", panel);
    }

    /**
     * 创建主机管理Tab
     */
    private void createHostManagementTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // 主机列表
        hostList = new JList<>(hostListModel);
        hostList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        JScrollPane hostScrollPane = new JScrollPane(hostList);
        hostScrollPane.setBorder(new TitledBorder("发现的主机"));

        // 控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        JButton clearButton = new JButton("清空");
        JButton refreshButton = new JButton("刷新");

        clearButton.addActionListener(e -> {
            discoveredHosts.clear();
            updateHostDisplay();
        });

        refreshButton.addActionListener(e -> updateHostDisplay());

        controlPanel.add(refreshButton);
        controlPanel.add(clearButton);

        panel.add(controlPanel, BorderLayout.NORTH);
        panel.add(hostScrollPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("🏠 Host Management", panel);
    }

    /**
     * 创建日志Tab
     */
    private void createLogTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // 日志显示区域
        logArea = new JTextArea(20, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setBorder(new TitledBorder("扩展日志"));

        // 控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        JButton clearButton = new JButton("清空日志");

        clearButton.addActionListener(e -> logArea.setText(""));

        controlPanel.add(clearButton);

        panel.add(controlPanel, BorderLayout.NORTH);
        panel.add(logScrollPane, BorderLayout.CENTER);

        mainTabbedPane.addTab("📋 Logs", panel);
    }

    /**
     * 创建配置Tab
     */
    private void createConfigurationTab() {
        JPanel panel = new JPanel(new BorderLayout());

        // 配置选项
        JPanel configPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();

        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        JCheckBox enabledCheckBox = new JCheckBox("启用扩展", isEnabled);
        enabledCheckBox.addActionListener(e -> {
            isEnabled = enabledCheckBox.isSelected();
            logging.logToOutput("Extension " + (isEnabled ? "enabled" : "disabled"));
        });
        configPanel.add(enabledCheckBox, gbc);

        gbc.gridy = 1;
        JCheckBox autoScanCheckBox = new JCheckBox("自动扫描", autoScanEnabled);
        autoScanCheckBox.addActionListener(e -> {
            autoScanEnabled = autoScanCheckBox.isSelected();
            logging.logToOutput("Auto scan " + (autoScanEnabled ? "enabled" : "disabled"));
        });
        configPanel.add(autoScanCheckBox, gbc);

        gbc.gridy = 2;
        JButton saveButton = new JButton("保存配置");
        saveButton.addActionListener(e -> saveConfiguration());
        configPanel.add(saveButton, gbc);

        panel.add(configPanel, BorderLayout.NORTH);

        mainTabbedPane.addTab("⚙️ Configuration", panel);
    }
    
    /**
     * HTTP请求处理 - 保留原有的所有处理逻辑
     */
    @Override
    public RequestToBeSentAction handleHttpRequestToBeSent(HttpRequestToBeSent requestToBeSent) {
        if (!isEnabled) {
            return RequestToBeSentAction.continueWith(requestToBeSent);
        }
        
        try {
            // 在后台处理请求分析，避免阻塞
            executorService.submit(() -> {
                try {
                    // HttpRequestToBeSent 继承自 HttpRequest
                    HttpRequest request = requestToBeSent;
                    
                    // 使用原有的处理逻辑
                    processHttpRequest(request);
                    
                } catch (Exception e) {
                    logging.logToError("Error analyzing request: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            logging.logToError("Error in handleHttpRequestToBeSent: " + e.getMessage());
        }
        
        return RequestToBeSentAction.continueWith(requestToBeSent);
    }
    
    /**
     * HTTP响应处理 - 保留原有的所有处理逻辑
     */
    @Override
    public ResponseReceivedAction handleHttpResponseReceived(HttpResponseReceived responseReceived) {
        if (!isEnabled || !autoScanEnabled) {
            return ResponseReceivedAction.continueWith(responseReceived);
        }
        
        try {
            // 在后台处理响应分析，避免阻塞
            executorService.submit(() -> {
                try {
                    HttpRequest request = responseReceived.initiatingRequest();
                    HttpResponse response = responseReceived;
                    
                    // 使用原有的处理逻辑
                    processHttpResponse(request, response);
                    
                } catch (Exception e) {
                    logging.logToError("Error analyzing response: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            logging.logToError("Error in handleHttpResponseReceived: " + e.getMessage());
        }
        
        return ResponseReceivedAction.continueWith(responseReceived);
    }
    
    /**
     * 处理HTTP请求 - 保留原有逻辑，适配Montoya API
     */
    private void processHttpRequest(HttpRequest request) {
        try {
            String url = request.url();
            String host = request.httpService().host();
            
            // 添加主机
            addDiscoveredHost(host);
            
            // 检查是否为API端点
            if (isApiEndpoint(url)) {
                String path = extractPath(url);
                addDiscoveredEndpoint(host, path);
                logging.logToOutput("发现API端点: " + url);
            }
            
            // 分析请求体中的API路径
            if (request.body().length() > 0) {
                String body = request.bodyToString();
                findApiPathsInContent(body, host);
            }
            
            // 敏感信息提取
            extractSensitiveInfoFromRequest(request);
            
        } catch (Exception e) {
            logging.logToError("Error processing HTTP request: " + e.getMessage());
        }
    }
    
    /**
     * 处理HTTP响应 - 保留原有逻辑，适配Montoya API
     */
    private void processHttpResponse(HttpRequest request, HttpResponse response) {
        try {
            String host = request.httpService().host();
            String url = request.url();
            
            // 添加主机
            addDiscoveredHost(host);
            
            // 检查响应中的API路径
            if (response.body().length() > 0) {
                String responseBody = response.bodyToString();
                findApiPathsInContent(responseBody, host);
            }
            
            // 如果是JavaScript文件，进行深度分析
            if (isJavaScriptFile(url)) {
                analyzeJavaScriptFile(response, host);
            }
            
            // 敏感信息提取
            extractSensitiveInfoFromResponse(response);
            
            // 更新UI - 通知各个Tab更新数据
            updateUIComponents();
            
        } catch (Exception e) {
            logging.logToError("Error processing HTTP response: " + e.getMessage());
        }
    }
    
    // 工具方法 - 保留原有逻辑
    private String extractPath(String url) {
        try {
            URL urlObj = new URL(url);
            return urlObj.getPath();
        } catch (Exception e) {
            return "";
        }
    }
    
    private boolean isApiEndpoint(String url) {
        String path = extractPath(url);
        return isValidApiPath(path);
    }
    
    private boolean isValidApiPath(String path) {
        if (path == null || path.length() <= 1) {
            return false;
        }
        
        String lowerPath = path.toLowerCase();
        return lowerPath.contains("/api/") || 
               lowerPath.matches("/v\\d+/.*") || 
               lowerPath.contains("/rest/") || 
               lowerPath.contains("/graphql");
    }
    
    private boolean isJavaScriptFile(String url) {
        return url.toLowerCase().matches(".*\\.js(\\?.*)?$");
    }
    
    private void analyzeJavaScriptFile(HttpResponse response, String host) {
        if (response.body().length() > 0) {
            String jsContent = response.bodyToString();
            findApiPathsInContent(jsContent, host);
        }
    }
    
    private void findApiPathsInContent(String content, String host) {
        // API路径发现的正则表达式模式
        Pattern[] apiPatterns = {
            Pattern.compile("[\"\\']/api/[a-zA-Z0-9_/\\-]*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("[\"\\']/v\\d+/[a-zA-Z0-9_/\\-]*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("[\"\\']/rest/[a-zA-Z0-9_/\\-]*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("[\"\\']/graphql[a-zA-Z0-9_/\\-]*", Pattern.CASE_INSENSITIVE),
            Pattern.compile("fetch\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("axios\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']", Pattern.CASE_INSENSITIVE)
        };

        Set<String> foundPaths = new HashSet<>();

        for (Pattern pattern : apiPatterns) {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String path = extractPathFromMatch(matcher);
                if (path != null && isValidApiPath(path)) {
                    foundPaths.add(path);
                }
            }
        }

        // 添加发现的路径
        for (String path : foundPaths) {
            addDiscoveredEndpoint(host, path);
        }

        if (!foundPaths.isEmpty()) {
            logging.logToOutput("在内容中发现 " + foundPaths.size() + " 个API路径");
        }
    }

    private String extractPathFromMatch(Matcher matcher) {
        String path = matcher.group();
        if (matcher.groupCount() > 0 && matcher.group(1) != null) {
            path = matcher.group(1);
        }
        path = path.replaceAll("[\"\']", "").trim();
        if (!path.startsWith("/")) {
            return null;
        }
        return path;
    }

    private void addDiscoveredHost(String host) {
        if (host != null && !host.isEmpty()) {
            boolean isNew = discoveredHosts.add(host);
            if (isNew) {
                logging.logToOutput("发现新主机: " + host);
            }
        }
    }

    private void addDiscoveredEndpoint(String host, String path) {
        if (host != null && path != null) {
            discoveredEndpoints.computeIfAbsent(host, k -> ConcurrentHashMap.newKeySet()).add(path);
            logging.logToOutput("发现新路径: " + host + path);
        }
    }

    private void extractSensitiveInfoFromRequest(HttpRequest request) {
        // 敏感信息提取的正则表达式模式
        Pattern[] sensitivePatterns = {
            Pattern.compile("password\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("token\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("key\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("secret\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("auth\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE)
        };

        String url = request.url();

        // 检查URL中的敏感信息
        for (Pattern pattern : sensitivePatterns) {
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                String info = "URL中发现敏感信息: " + matcher.group() + " 在 " + request.httpService().host();
                addSensitiveInfo(info);
            }
        }

        // 检查请求体中的敏感信息
        if (request.body().length() > 0) {
            String body = request.bodyToString();
            for (Pattern pattern : sensitivePatterns) {
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    String info = "请求体中发现敏感信息: " + pattern.pattern() + " 在 " + request.httpService().host();
                    addSensitiveInfo(info);
                }
            }
        }
    }

    private void extractSensitiveInfoFromResponse(HttpResponse response) {
        // 敏感信息提取的正则表达式模式
        Pattern[] sensitivePatterns = {
            Pattern.compile("password\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("token\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("key\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("secret\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE),
            Pattern.compile("auth\\s*[:=]\\s*[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE)
        };

        if (response.body().length() > 0) {
            String body = response.bodyToString();
            for (Pattern pattern : sensitivePatterns) {
                Matcher matcher = pattern.matcher(body);
                if (matcher.find()) {
                    String info = "响应中发现敏感信息: " + pattern.pattern();
                    addSensitiveInfo(info);
                }
            }
        }
    }

    private void addSensitiveInfo(String info) {
        if (info != null && !sensitiveInfo.contains(info)) {
            sensitiveInfo.add(info);
            logging.logToOutput("发现敏感信息: " + info);
        }
    }
    
    private void updateUIComponents() {
        SwingUtilities.invokeLater(() -> {
            try {
                updateEndpointsDisplay();
                updateSensitiveInfoDisplay();
                updateHostDisplay();
            } catch (Exception e) {
                logging.logToError("Error updating UI components: " + e.getMessage());
            }
        });
    }

    /**
     * 更新端点显示
     */
    private void updateEndpointsDisplay() {
        if (discoveredEndpointsArea != null) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Set<String>> entry : discoveredEndpoints.entrySet()) {
                String host = entry.getKey();
                for (String path : entry.getValue()) {
                    sb.append(host).append(path).append("\n");
                }
            }
            discoveredEndpointsArea.setText(sb.toString());
        }
    }

    /**
     * 更新敏感信息显示
     */
    private void updateSensitiveInfoDisplay() {
        if (sensitiveInfoArea != null) {
            StringBuilder sb = new StringBuilder();
            for (String info : sensitiveInfo) {
                sb.append(info).append("\n");
            }
            sensitiveInfoArea.setText(sb.toString());
        }
    }

    /**
     * 更新主机显示
     */
    private void updateHostDisplay() {
        if (hostListModel != null) {
            hostListModel.clear();
            for (String host : discoveredHosts) {
                hostListModel.addElement(host);
            }
        }
    }

    /**
     * 导出端点
     */
    private void exportEndpoints() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出API端点");
        fileChooser.setSelectedFile(new java.io.File("api_endpoints.txt"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try (java.io.PrintWriter writer = new java.io.PrintWriter(fileChooser.getSelectedFile())) {
                for (Map.Entry<String, Set<String>> entry : discoveredEndpoints.entrySet()) {
                    String host = entry.getKey();
                    for (String path : entry.getValue()) {
                        writer.println(host + path);
                    }
                }
                logging.logToOutput("API endpoints exported to: " + fileChooser.getSelectedFile().getAbsolutePath());
            } catch (Exception e) {
                logging.logToError("Failed to export endpoints: " + e.getMessage());
            }
        }
    }

    /**
     * 导出敏感信息
     */
    private void exportSensitiveInfo() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出敏感信息");
        fileChooser.setSelectedFile(new java.io.File("sensitive_info.txt"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try (java.io.PrintWriter writer = new java.io.PrintWriter(fileChooser.getSelectedFile())) {
                for (String info : sensitiveInfo) {
                    writer.println(info);
                }
                logging.logToOutput("Sensitive info exported to: " + fileChooser.getSelectedFile().getAbsolutePath());
            } catch (Exception e) {
                logging.logToError("Failed to export sensitive info: " + e.getMessage());
            }
        }
    }
    
    /**
     * 加载配置 - 使用Montoya Persistence API
     */
    private void loadConfiguration() {
        try {
            PersistedObject extensionData = persistence.extensionData();
            
            // 官方API不支持默认值参数，需要手动处理
            Boolean enabled = extensionData.getBoolean("enabled");
            isEnabled = (enabled != null) ? enabled : true;
            
            Boolean autoScan = extensionData.getBoolean("autoScanEnabled");
            autoScanEnabled = (autoScan != null) ? autoScan : true;
            
            logging.logToOutput("Configuration loaded successfully");
            
        } catch (Exception e) {
            logging.logToError("Error loading configuration: " + e.getMessage());
        }
    }
    
    /**
     * 保存配置
     */
    private void saveConfiguration() {
        try {
            PersistedObject extensionData = persistence.extensionData();
            extensionData.setBoolean("enabled", isEnabled);
            extensionData.setBoolean("autoScanEnabled", autoScanEnabled);
            
            logging.logToOutput("Configuration saved successfully");
            
        } catch (Exception e) {
            logging.logToError("Error saving configuration: " + e.getMessage());
        }
    }
    
    /**
     * 扩展卸载处理
     */
    @Override
    public void extensionUnloaded() {
        try {
            logging.logToOutput("API Security Checker extension is being unloaded...");
            
            // 保存配置
            saveConfiguration();
            
            // 停止所有后台任务
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            logging.logToOutput("API Security Checker extension unloaded successfully");
            
        } catch (Exception e) {
            logging.logToError("Error during extension unloading: " + e.getMessage());
        }
    }
    
    // 公共API方法
    public void setEnabled(boolean enabled) {
        this.isEnabled = enabled;
        logging.logToOutput("API Security Checker " + (enabled ? "enabled" : "disabled"));
    }
    
    public void setAutoScanEnabled(boolean enabled) {
        this.autoScanEnabled = enabled;
        logging.logToOutput("Auto scan " + (enabled ? "enabled" : "disabled"));
    }
    
    public Set<String> getDiscoveredHosts() {
        return new HashSet<>(discoveredHosts);
    }
    
    public Map<String, Set<String>> getDiscoveredEndpoints() {
        return new HashMap<>(discoveredEndpoints);
    }
    
    public List<String> getSensitiveInfo() {
        return new ArrayList<>(sensitiveInfo);
    }
    
    public MontoyaApi getApi() {
        return api;
    }
}
