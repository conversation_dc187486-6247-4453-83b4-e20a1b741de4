package burp;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.Properties;

/**
 * 配置Tab - 提供扫描配置和设置管理
 */
public class ConfigurationTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    
    // 扫描配置
    private JCheckBox autoAnalyzeJsCheckBox;
    private JCheckBox extractSensitiveInfoCheckBox;
    private JCheckBox discoverApiPathsCheckBox;
    private JCheckBox realTimeMonitorCheckBox;
    private JTextField timeoutField;
    private JTextField concurrencyField;
    private JTextField maxDepthField;
    
    // 过滤配置
    private JTextArea includeDomainsArea;
    private JTextArea excludePathsArea;
    private JTextArea fileTypesArea;
    private JTextArea customPatternsArea;
    
    // 高级配置
    private JCheckBox enableProxyCheckBox;
    private JTextField proxyHostField;
    private JTextField proxyPortField;
    private JCheckBox enableLoggingCheckBox;
    private JComboBox<String> logLevelCombo;
    private JTextField userAgentField;
    
    // 配置存储
    private Properties config;
    
    public ConfigurationTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.config = new Properties();
        
        initializeUI();
        loadDefaultConfig();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建选项卡面板
        JTabbedPane configTabs = new JTabbedPane();
        
        // 扫描配置选项卡
        configTabs.addTab("扫描配置", createScanConfigPanel());
        
        // 过滤配置选项卡
        configTabs.addTab("过滤配置", createFilterConfigPanel());
        
        // 高级配置选项卡
        configTabs.addTab("高级配置", createAdvancedConfigPanel());
        
        mainPanel.add(configTabs, BorderLayout.CENTER);
        
        // 创建底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建扫描配置面板
     */
    private JPanel createScanConfigPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 功能开关组
        JPanel featuresPanel = new JPanel(new GridLayout(4, 1, 5, 5));
        featuresPanel.setBorder(BorderFactory.createTitledBorder("功能开关"));
        
        autoAnalyzeJsCheckBox = new JCheckBox("自动分析JS文件", true);
        autoAnalyzeJsCheckBox.setToolTipText("自动分析发现的JavaScript文件以提取API路径");
        featuresPanel.add(autoAnalyzeJsCheckBox);
        
        extractSensitiveInfoCheckBox = new JCheckBox("提取敏感信息", true);
        extractSensitiveInfoCheckBox.setToolTipText("在分析过程中自动提取敏感信息");
        featuresPanel.add(extractSensitiveInfoCheckBox);
        
        discoverApiPathsCheckBox = new JCheckBox("发现API路径", true);
        discoverApiPathsCheckBox.setToolTipText("从JavaScript代码中发现API端点");
        featuresPanel.add(discoverApiPathsCheckBox);
        
        realTimeMonitorCheckBox = new JCheckBox("实时监控", false);
        realTimeMonitorCheckBox.setToolTipText("实时监控HTTP流量并自动分析");
        featuresPanel.add(realTimeMonitorCheckBox);
        
        panel.add(featuresPanel);
        panel.add(Box.createVerticalStrut(10));
        
        // 性能配置组
        JPanel performancePanel = new JPanel(new GridBagLayout());
        performancePanel.setBorder(BorderFactory.createTitledBorder("性能配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        gbc.gridx = 0; gbc.gridy = 0;
        performancePanel.add(new JLabel("超时时间(秒):"), gbc);
        gbc.gridx = 1;
        timeoutField = new JTextField("30", 10);
        performancePanel.add(timeoutField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1;
        performancePanel.add(new JLabel("并发数:"), gbc);
        gbc.gridx = 1;
        concurrencyField = new JTextField("5", 10);
        performancePanel.add(concurrencyField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 2;
        performancePanel.add(new JLabel("最大深度:"), gbc);
        gbc.gridx = 1;
        maxDepthField = new JTextField("3", 10);
        maxDepthField.setToolTipText("JavaScript文件分析的最大递归深度");
        performancePanel.add(maxDepthField, gbc);
        
        panel.add(performancePanel);
        
        return panel;
    }
    
    /**
     * 创建过滤配置面板
     */
    private JPanel createFilterConfigPanel() {
        JPanel panel = new JPanel(new GridLayout(2, 2, 10, 10));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 包含域名
        JPanel includePanel = new JPanel(new BorderLayout());
        includePanel.setBorder(BorderFactory.createTitledBorder("包含域名"));
        includeDomainsArea = new JTextArea(8, 30);
        includeDomainsArea.setToolTipText("每行一个域名模式，支持通配符\n例如: *.example.com");
        includeDomainsArea.setText("*.example.com\n*.api.example.com");
        JScrollPane includeScrollPane = new JScrollPane(includeDomainsArea);
        includePanel.add(includeScrollPane, BorderLayout.CENTER);
        panel.add(includePanel);
        
        // 排除路径
        JPanel excludePanel = new JPanel(new BorderLayout());
        excludePanel.setBorder(BorderFactory.createTitledBorder("排除路径"));
        excludePathsArea = new JTextArea(8, 30);
        excludePathsArea.setToolTipText("每行一个路径模式，支持通配符\n例如: /static/*");
        excludePathsArea.setText("/static/*\n/assets/*\n*.css\n*.jpg\n*.png");
        JScrollPane excludeScrollPane = new JScrollPane(excludePathsArea);
        excludePanel.add(excludeScrollPane, BorderLayout.CENTER);
        panel.add(excludePanel);
        
        // 文件类型
        JPanel fileTypesPanel = new JPanel(new BorderLayout());
        fileTypesPanel.setBorder(BorderFactory.createTitledBorder("分析文件类型"));
        fileTypesArea = new JTextArea(8, 30);
        fileTypesArea.setToolTipText("每行一个文件扩展名");
        fileTypesArea.setText(".js\n.json\n.xml\n.html\n.htm");
        JScrollPane fileTypesScrollPane = new JScrollPane(fileTypesArea);
        fileTypesPanel.add(fileTypesScrollPane, BorderLayout.CENTER);
        panel.add(fileTypesPanel);
        
        // 自定义模式
        JPanel customPanel = new JPanel(new BorderLayout());
        customPanel.setBorder(BorderFactory.createTitledBorder("自定义正则模式"));
        customPatternsArea = new JTextArea(8, 30);
        customPatternsArea.setToolTipText("每行一个正则表达式，用于发现API路径");
        customPatternsArea.setText("[\"\\']/api/[a-zA-Z0-9_/\\-]*[\"\\']\n[\"\\']/v\\d+/[a-zA-Z0-9_/\\-]*[\"\\'']");
        JScrollPane customScrollPane = new JScrollPane(customPatternsArea);
        customPanel.add(customScrollPane, BorderLayout.CENTER);
        panel.add(customPanel);
        
        return panel;
    }
    
    /**
     * 创建高级配置面板
     */
    private JPanel createAdvancedConfigPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 代理配置
        JPanel proxyPanel = new JPanel(new GridBagLayout());
        proxyPanel.setBorder(BorderFactory.createTitledBorder("代理配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        enableProxyCheckBox = new JCheckBox("启用代理", false);
        enableProxyCheckBox.addActionListener(e -> toggleProxyFields());
        proxyPanel.add(enableProxyCheckBox, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        proxyPanel.add(new JLabel("代理主机:"), gbc);
        gbc.gridx = 1;
        proxyHostField = new JTextField("127.0.0.1", 15);
        proxyHostField.setEnabled(false);
        proxyPanel.add(proxyHostField, gbc);
        
        gbc.gridx = 0; gbc.gridy = 2;
        proxyPanel.add(new JLabel("代理端口:"), gbc);
        gbc.gridx = 1;
        proxyPortField = new JTextField("8080", 15);
        proxyPortField.setEnabled(false);
        proxyPanel.add(proxyPortField, gbc);
        
        panel.add(proxyPanel);
        panel.add(Box.createVerticalStrut(10));
        
        // 日志配置
        JPanel loggingPanel = new JPanel(new GridBagLayout());
        loggingPanel.setBorder(BorderFactory.createTitledBorder("日志配置"));
        gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2;
        enableLoggingCheckBox = new JCheckBox("启用详细日志", true);
        loggingPanel.add(enableLoggingCheckBox, gbc);
        
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 1;
        loggingPanel.add(new JLabel("日志级别:"), gbc);
        gbc.gridx = 1;
        logLevelCombo = new JComboBox<>(new String[]{"DEBUG", "INFO", "WARN", "ERROR"});
        logLevelCombo.setSelectedItem("INFO");
        loggingPanel.add(logLevelCombo, gbc);
        
        panel.add(loggingPanel);
        panel.add(Box.createVerticalStrut(10));
        
        // HTTP配置
        JPanel httpPanel = new JPanel(new GridBagLayout());
        httpPanel.setBorder(BorderFactory.createTitledBorder("HTTP配置"));
        gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        gbc.gridx = 0; gbc.gridy = 0;
        httpPanel.add(new JLabel("User-Agent:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        userAgentField = new JTextField("Mozilla/5.0 (API Security Checker)", 30);
        httpPanel.add(userAgentField, gbc);
        
        panel.add(httpPanel);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JButton saveButton = new JButton("💾 保存配置");
        saveButton.addActionListener(e -> saveConfig());
        buttonPanel.add(saveButton);
        
        JButton loadButton = new JButton("📁 加载配置");
        loadButton.addActionListener(e -> loadConfig());
        buttonPanel.add(loadButton);
        
        JButton resetButton = new JButton("🔄 重置默认");
        resetButton.addActionListener(e -> resetToDefault());
        buttonPanel.add(resetButton);
        
        JButton exportButton = new JButton("📤 导出配置");
        exportButton.addActionListener(e -> exportConfig());
        buttonPanel.add(exportButton);
        
        JButton importButton = new JButton("📥 导入配置");
        importButton.addActionListener(e -> importConfig());
        buttonPanel.add(importButton);
        
        return buttonPanel;
    }
    
    /**
     * 切换代理字段状态
     */
    private void toggleProxyFields() {
        boolean enabled = enableProxyCheckBox.isSelected();
        proxyHostField.setEnabled(enabled);
        proxyPortField.setEnabled(enabled);
    }
    
    /**
     * 加载默认配置
     */
    private void loadDefaultConfig() {
        // 设置默认值已在UI创建时完成
        callbacks.printOutput("已加载默认配置");
    }
    
    /**
     * 保存配置
     */
    private void saveConfig() {
        try {
            // 扫描配置
            config.setProperty("autoAnalyzeJs", String.valueOf(autoAnalyzeJsCheckBox.isSelected()));
            config.setProperty("extractSensitiveInfo", String.valueOf(extractSensitiveInfoCheckBox.isSelected()));
            config.setProperty("discoverApiPaths", String.valueOf(discoverApiPathsCheckBox.isSelected()));
            config.setProperty("realTimeMonitor", String.valueOf(realTimeMonitorCheckBox.isSelected()));
            config.setProperty("timeout", timeoutField.getText());
            config.setProperty("concurrency", concurrencyField.getText());
            config.setProperty("maxDepth", maxDepthField.getText());
            
            // 过滤配置
            config.setProperty("includeDomains", includeDomainsArea.getText());
            config.setProperty("excludePaths", excludePathsArea.getText());
            config.setProperty("fileTypes", fileTypesArea.getText());
            config.setProperty("customPatterns", customPatternsArea.getText());
            
            // 高级配置
            config.setProperty("enableProxy", String.valueOf(enableProxyCheckBox.isSelected()));
            config.setProperty("proxyHost", proxyHostField.getText());
            config.setProperty("proxyPort", proxyPortField.getText());
            config.setProperty("enableLogging", String.valueOf(enableLoggingCheckBox.isSelected()));
            config.setProperty("logLevel", (String) logLevelCombo.getSelectedItem());
            config.setProperty("userAgent", userAgentField.getText());
            
            callbacks.printOutput("配置已保存");
            JOptionPane.showMessageDialog(mainPanel, "配置保存成功！", "保存配置", JOptionPane.INFORMATION_MESSAGE);
            
        } catch (Exception e) {
            callbacks.printError("保存配置失败: " + e.getMessage());
            JOptionPane.showMessageDialog(mainPanel, "保存配置失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 加载配置
     */
    private void loadConfig() {
        try {
            // 扫描配置
            autoAnalyzeJsCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("autoAnalyzeJs", "true")));
            extractSensitiveInfoCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("extractSensitiveInfo", "true")));
            discoverApiPathsCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("discoverApiPaths", "true")));
            realTimeMonitorCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("realTimeMonitor", "false")));
            timeoutField.setText(config.getProperty("timeout", "30"));
            concurrencyField.setText(config.getProperty("concurrency", "5"));
            maxDepthField.setText(config.getProperty("maxDepth", "3"));
            
            // 过滤配置
            includeDomainsArea.setText(config.getProperty("includeDomains", "*.example.com\n*.api.example.com"));
            excludePathsArea.setText(config.getProperty("excludePaths", "/static/*\n/assets/*\n*.css\n*.jpg\n*.png"));
            fileTypesArea.setText(config.getProperty("fileTypes", ".js\n.json\n.xml\n.html\n.htm"));
            customPatternsArea.setText(config.getProperty("customPatterns", "[\"\\']/api/[a-zA-Z0-9_/\\-]*[\"\\']\n[\"\\']/v\\d+/[a-zA-Z0-9_/\\-]*[\"\\'']"));
            
            // 高级配置
            enableProxyCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("enableProxy", "false")));
            proxyHostField.setText(config.getProperty("proxyHost", "127.0.0.1"));
            proxyPortField.setText(config.getProperty("proxyPort", "8080"));
            enableLoggingCheckBox.setSelected(Boolean.parseBoolean(config.getProperty("enableLogging", "true")));
            logLevelCombo.setSelectedItem(config.getProperty("logLevel", "INFO"));
            userAgentField.setText(config.getProperty("userAgent", "Mozilla/5.0 (API Security Checker)"));
            
            toggleProxyFields();
            
            callbacks.printOutput("配置已加载");
            JOptionPane.showMessageDialog(mainPanel, "配置加载成功！", "加载配置", JOptionPane.INFORMATION_MESSAGE);
            
        } catch (Exception e) {
            callbacks.printError("加载配置失败: " + e.getMessage());
            JOptionPane.showMessageDialog(mainPanel, "加载配置失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 重置为默认配置
     */
    private void resetToDefault() {
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "确定要重置为默认配置吗？这将丢失当前所有设置。",
            "重置配置",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            config.clear();
            loadDefaultConfig();
            loadConfig();
            callbacks.printOutput("已重置为默认配置");
        }
    }
    
    /**
     * 导出配置
     */
    private void exportConfig() {
        // TODO: 实现配置导出功能
        callbacks.printOutput("导出配置功能待实现");
        JOptionPane.showMessageDialog(mainPanel, "导出配置功能待实现", "导出配置", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 导入配置
     */
    private void importConfig() {
        // TODO: 实现配置导入功能
        callbacks.printOutput("导入配置功能待实现");
        JOptionPane.showMessageDialog(mainPanel, "导入配置功能待实现", "导入配置", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 获取配置值
     */
    public String getConfigValue(String key, String defaultValue) {
        return config.getProperty(key, defaultValue);
    }
    
    /**
     * 设置配置值
     */
    public void setConfigValue(String key, String value) {
        config.setProperty(key, value);
    }
    
    /**
     * 检查功能是否启用
     */
    public boolean isFeatureEnabled(String feature) {
        switch (feature) {
            case "autoAnalyzeJs":
                return autoAnalyzeJsCheckBox.isSelected();
            case "extractSensitiveInfo":
                return extractSensitiveInfoCheckBox.isSelected();
            case "discoverApiPaths":
                return discoverApiPathsCheckBox.isSelected();
            case "realTimeMonitor":
                return realTimeMonitorCheckBox.isSelected();
            case "enableProxy":
                return enableProxyCheckBox.isSelected();
            case "enableLogging":
                return enableLoggingCheckBox.isSelected();
            default:
                return false;
        }
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        // 配置Tab不需要刷新数据
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        // 配置Tab不需要清除数据
    }
    
    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
