package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * API发现Tab - 集成原始API扫描功能到新的API扫描界面中
 * 提供API发现和JavaScript分析功能
 */
public class ApiDiscoveryTab {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JTextField urlField;
    private JTextArea customHeadersField;
    private JComboBox<String> attackTypeBox;
    private JButton startDiscoveryButton;
    private JCheckBox syncAnalyzeCheckBox;
    
    // 结果显示
    private JTextArea outputArea;
    private JTable resultsTable;
    private DefaultTableModel tableModel;
    
    // 数据 - 集成原始API发现的数据结构
    private List<DiscoveryResult> discoveryResults;
    private Set<String> apiPaths = new HashSet<>();
    private Set<String> jsUrls = new HashSet<>();
    private Map<String, List<String>> sensitiveData = new HashMap<>();
    private Map<String, String> sensitiveDataSources = new HashMap<>();
    private Map<String, String> apiPathSources = new HashMap<>(); // 记录API路径的来源URL
    private Map<String, Set<String>> jsDependencyGraph = new HashMap<>(); // 记录JS文件依赖关系图
    private Set<String> processedSensitiveInfo = new HashSet<>(); // 记录已处理的敏感信息（用于去重）

    // 敏感信息正则表达式 (完整版本，基于Chrome扩展)
    private static final Pattern[] SENSITIVE_PATTERNS = {
        // 身份证号
        Pattern.compile("[\"']((?:\\d{8}(?:0\\d|10|11|12)(?:[0-2]\\d|30|31)\\d{3})|(?:\\d{6}(?:18|19|20)\\d{2}(?:0[1-9]|10|11|12)(?:[0-2]\\d|30|31)\\d{3}(?:\\d|X|x)))[\"']"),
        // 手机号
        Pattern.compile("[\"'](?:1(?:3(?:[0-35-9]\\d|4[1-8])|4[14-9]\\d|5[\\d]\\d|66\\d|7[2-35-8]\\d|8\\d{2}|9[89]\\d)\\d{7})[\"']"),
        // 邮箱
        Pattern.compile("[\"'][a-zA-Z0-9\\._\\-]*@[a-zA-Z0-9\\._\\-]{1,63}\\.(?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,}[\"']"),
        // IP地址
        Pattern.compile("[\"'](?:(?:[a-zA-Z0-9]+:)?//)?\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(?:/.*?)?[\"']"),
        // IP+端口
        Pattern.compile("[\"'](?:(?:[a-zA-Z0-9]+:)?//)?\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}:\\d{1,5}(?:/.*?)?[\"']"),
        // 域名
        Pattern.compile("[\"'](?:(?:[a-zA-Z0-9]+:)?//)?[a-zA-Z0-9\\-\\.]*?\\.(?:xin|com|cn|net|com\\.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net\\.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org\\.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw)(?:\\d{1,5})?(?:/.*?)?[\"']"),
        // JWT Token
        Pattern.compile("[\"'](?:ey[A-Za-z0-9_-]{10,}\\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_/+-]{10,}\\.[A-Za-z0-9._/+-]{10,})[\"']"),
        // 算法相关
        Pattern.compile("\\W(?:Base64\\.encode|Base64\\.decode|btoa|atob|CryptoJS\\.AES|CryptoJS\\.DES|JSEncrypt|rsa|KJUR|\\$\\.md5|md5|sha1|sha256|sha512)[\\(\\.]"),
        // API Keys
        Pattern.compile("[\"']?[Aa]uthorization[\"']?\\s*[:=]\\s*[\"']?\\b(?:[Tt]oken\\s+)?[a-zA-Z0-9\\-_+/]{20,500}[\"']?"),
        // AWS Keys
        Pattern.compile("(?:AKLT|AKTP)[a-zA-Z0-9]{35,50}"),
        Pattern.compile("(?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}"),
        // GitHub Tokens
        Pattern.compile("(?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255}"),
        // Google API Key
        Pattern.compile("AIza[0-9A-Za-z_\\-]{35}"),
        // Bearer Token
        Pattern.compile("[Bb]earer\\s+[a-zA-Z0-9\\-=._+/\\\\]{20,500}"),
        // Basic Auth
        Pattern.compile("[Bb]asic\\s+[A-Za-z0-9+/]{18,}={0,2}"),
        // 密码相关
        Pattern.compile("(?:admin_?pass|password|[a-z]{3,15}_?password|user_?pass|user_?pwd|admin_?pwd)\\\\?['\"]\\s*[:=]\\s*\\\\?['\"][a-z0-9!@#$%&*]{5,20}\\\\?['\"]", Pattern.CASE_INSENSITIVE),
        // 数据库连接
        Pattern.compile("jdbc:[a-zA-Z0-9]+://[^\\s\"'<>]+"),
        // 私钥
        Pattern.compile("-----BEGIN\\s+\\w+\\s+PRIVATE\\s+KEY-----"),
        // Webhook URLs
        Pattern.compile("https://(?:qyapi\\.weixin\\.qq\\.com/cgi-bin/webhook/send\\?key=[a-zA-Z0-9\\-]{25,50}|oapi\\.dingtalk\\.com/robot/send\\?access_token=[a-z0-9]{50,80}|open\\.feishu\\.cn/open-apis/bot/v2/hook/[a-z0-9\\-]{25,50}|hooks\\.slack\\.com/services/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{15,24})"),
        // Secret Keys
        Pattern.compile("[\"']?[\\w_-]*?(?:secret|password|token|key|api[_-]?key)[\\w_-]*?[\"']?\\s*[:=]\\s*[\"']?[\\w\\-/+=]{10,}[\"']?", Pattern.CASE_INSENSITIVE)
    };

    // 敏感信息类型名称
    private static final String[] SENSITIVE_TYPES = {
        "身份证号", "手机号", "邮箱地址", "IP地址", "IP+端口", "域名", "JWT Token",
        "加密算法", "Authorization Token", "AWS Access Key", "AWS Key ID",
        "GitHub Token", "Google API Key", "Bearer Token", "Basic Auth",
        "密码配置", "数据库连接", "私钥", "Webhook URL", "密钥配置"
    };
    
    public ApiDiscoveryTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.mainTab = mainTab;
        this.discoveryResults = new ArrayList<>();
        
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建顶部配置面板
        JPanel configPanel = createConfigPanel();
        mainPanel.add(configPanel, BorderLayout.NORTH);
        
        // 创建主要内容区域
        JSplitPane splitPane = createMainContent();
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建配置面板
     */
    private JPanel createConfigPanel() {
        JPanel configPanel = new JPanel();
        configPanel.setLayout(new BoxLayout(configPanel, BoxLayout.Y_AXIS));
        configPanel.setBorder(BorderFactory.createTitledBorder("API发现配置"));
        
        // 基本配置
        JPanel basicConfigPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 目标URL
        gbc.gridx = 0; gbc.gridy = 0;
        basicConfigPanel.add(new JLabel("目标URL:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        urlField = new JTextField();
        urlField.setToolTipText("输入要扫描的目标URL，例如: https://example.com");
        basicConfigPanel.add(urlField, gbc);
        
        // 扫描类型
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        basicConfigPanel.add(new JLabel("扫描类型:"), gbc);
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        attackTypeBox = new JComboBox<>(new String[]{"仅收集", "收集+测试"});
        basicConfigPanel.add(attackTypeBox, gbc);
        
        // 同步分析选项
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        syncAnalyzeCheckBox = new JCheckBox("同步分析敏感信息");
        syncAnalyzeCheckBox.setSelected(true);
        syncAnalyzeCheckBox.setToolTipText("在API发现过程中同时提取敏感信息");
        basicConfigPanel.add(syncAnalyzeCheckBox, gbc);
        
        // 开始按钮
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        gbc.anchor = GridBagConstraints.CENTER;
        startDiscoveryButton = new JButton("🔍 开始API发现");
        startDiscoveryButton.addActionListener(e -> startApiDiscovery());
        basicConfigPanel.add(startDiscoveryButton, gbc);
        
        configPanel.add(basicConfigPanel);
        
        // 自定义头部
        JPanel headersPanel = new JPanel(new BorderLayout());
        headersPanel.setBorder(BorderFactory.createTitledBorder("自定义HTTP头"));
        
        customHeadersField = new JTextArea(3, 50);
        customHeadersField.setToolTipText("每行一个头部，格式: Header-Name: Value");
        customHeadersField.setText("User-Agent: Mozilla/5.0 (API Security Checker)\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        JScrollPane headersScrollPane = new JScrollPane(customHeadersField);
        headersPanel.add(headersScrollPane, BorderLayout.CENTER);
        
        configPanel.add(headersPanel);
        
        return configPanel;
    }
    
    /**
     * 创建主要内容区域
     */
    private JSplitPane createMainContent() {
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setResizeWeight(0.6);
        
        // 左侧：输出日志
        JPanel outputPanel = new JPanel(new BorderLayout());
        outputPanel.setBorder(BorderFactory.createTitledBorder("发现日志"));
        
        outputArea = new JTextArea();
        outputArea.setEditable(false);
        outputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        outputArea.setText("API发现日志将在这里显示...\n");
        JScrollPane outputScrollPane = new JScrollPane(outputArea);
        outputPanel.add(outputScrollPane, BorderLayout.CENTER);
        
        splitPane.setLeftComponent(outputPanel);
        
        // 右侧：发现结果
        JPanel resultsPanel = createResultsPanel();
        splitPane.setRightComponent(resultsPanel);
        
        return splitPane;
    }
    
    /**
     * 创建结果面板
     */
    private JPanel createResultsPanel() {
        JPanel resultsPanel = new JPanel(new BorderLayout());
        resultsPanel.setBorder(BorderFactory.createTitledBorder("发现结果"));
        
        // 创建表格
        String[] columnNames = {"类型", "内容", "来源", "状态"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        resultsTable = new JTable(tableModel);
        resultsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        resultsTable.getTableHeader().setReorderingAllowed(false);
        
        // 设置列宽
        resultsTable.getColumnModel().getColumn(0).setPreferredWidth(80);  // 类型
        resultsTable.getColumnModel().getColumn(1).setPreferredWidth(200); // 内容
        resultsTable.getColumnModel().getColumn(2).setPreferredWidth(150); // 来源
        resultsTable.getColumnModel().getColumn(3).setPreferredWidth(80);  // 状态
        
        JScrollPane tableScrollPane = new JScrollPane(resultsTable);
        resultsPanel.add(tableScrollPane, BorderLayout.CENTER);
        
        // 操作按钮
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        JButton clearButton = new JButton("🗑️ 清除结果");
        clearButton.addActionListener(e -> clearResults());
        buttonPanel.add(clearButton);
        
        JButton exportButton = new JButton("📤 导出结果");
        exportButton.addActionListener(e -> exportResults());
        buttonPanel.add(exportButton);
        
        JButton syncToTabsButton = new JButton("🔄 同步到其他Tab");
        syncToTabsButton.addActionListener(e -> syncToOtherTabs());
        buttonPanel.add(syncToTabsButton);
        
        resultsPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        return resultsPanel;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JLabel statusLabel = new JLabel("就绪 - 等待开始API发现");
        statusPanel.add(statusLabel, BorderLayout.WEST);
        
        // 统计信息
        JLabel statsLabel = new JLabel("发现: 0 个API路径, 0 个敏感信息");
        statusPanel.add(statsLabel, BorderLayout.EAST);
        
        return statusPanel;
    }
    
    /**
     * 开始API发现
     */
    private void startApiDiscovery() {
        String targetUrl = urlField.getText().trim();
        if (targetUrl.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入目标URL", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        startDiscoveryButton.setEnabled(false);
        outputArea.append("开始API发现: " + targetUrl + "\n");
        
        // 在后台线程中执行发现
        new Thread(() -> {
            try {
                performApiDiscovery(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    outputArea.append("发现错误: " + e.getMessage() + "\n");
                    callbacks.printError("API discovery error: " + e.getMessage());
                });
            } finally {
                SwingUtilities.invokeLater(() -> startDiscoveryButton.setEnabled(true));
            }
        }).start();
    }
    
    /**
     * 执行API发现（完整递归发现逻辑）
     */
    private void performApiDiscovery(String targetUrl) {
        SwingUtilities.invokeLater(() -> outputArea.append("Phase 1: 开始完整API发现...\n"));

        try {
            // Ensure we're not in the EDT when making HTTP requests
            if (SwingUtilities.isEventDispatchThread()) {
                callbacks.printError("ERROR: HTTP request attempted in EDT!");
                SwingUtilities.invokeLater(() ->
                    outputArea.append("ERROR: Cannot make HTTP requests in Event Dispatch Thread\n"));
                return;
            }

            // 清空之前的结果
            apiPaths.clear();
            jsUrls.clear();
            sensitiveData.clear();
            sensitiveDataSources.clear();
            apiPathSources.clear();
            jsDependencyGraph.clear();
            processedSensitiveInfo.clear(); // 清空去重记录

            // 检查输入的URL是否是JavaScript文件
            if (targetUrl.endsWith(".js")) {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("检测到JavaScript文件URL，开始递归分析...\n"));

                // 开始递归分析JavaScript文件
                startRecursiveJsAnalysis(targetUrl);
                return;
            }

            // Phase 1: 分析网页响应
            SwingUtilities.invokeLater(() -> outputArea.append("Phase 1.1: 分析网页响应内容...\n"));

            // Create HTTP service
            URL url = new URL(targetUrl);
            IHttpService httpService = helpers.buildHttpService(url.getHost(), getEffectivePort(url), url.getProtocol());

            // Build request
            String path = url.getPath();
            if (path == null || path.isEmpty()) {
                path = "/";
            }
            String requestLine = "GET " + path +
                (url.getQuery() != null ? "?" + url.getQuery() : "") + " HTTP/1.1\r\n";
            String headers = buildHttpHeaders(url.getHost(),
                                            "Mozilla/5.0 (API Security Checker)",
                                            "text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8");

            headers += "\r\n";
            byte[] request = (requestLine + headers).getBytes();

            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, request);

            if (requestResponse.getResponse() != null) {
                String response = new String(requestResponse.getResponse());

                // 从网页响应中直接提取API路径
                SwingUtilities.invokeLater(() -> outputArea.append("Phase 1.2: 从网页响应中提取API路径...\n"));
                findApiPaths(response, targetUrl);

                // 如果启用了同步分析，提取主页面的敏感信息
                if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                    extractSensitiveInfo(response, targetUrl);
                }

                // Phase 2: 分析内联JavaScript
                SwingUtilities.invokeLater(() -> outputArea.append("Phase 2.1: 分析内联JavaScript...\n"));
                analyzeInlineJavaScript(response, targetUrl);

                // Phase 3: 发现外部JavaScript文件
                SwingUtilities.invokeLater(() -> outputArea.append("Phase 3.1: 发现外部JavaScript文件...\n"));
                Set<String> foundJsUrls = findJavaScriptUrls(response, targetUrl);

                SwingUtilities.invokeLater(() -> {
                    outputArea.append("发现 " + foundJsUrls.size() + " 个外部JavaScript文件:\n");
                    for (String jsUrl : foundJsUrls) {
                        outputArea.append("  - " + jsUrl + "\n");
                    }
                });

                // Phase 4: 递归分析所有JavaScript文件
                if (!foundJsUrls.isEmpty()) {
                    SwingUtilities.invokeLater(() -> outputArea.append("Phase 4: 开始递归分析JavaScript文件...\n"));
                    startRecursiveJsAnalysisForMultiple(foundJsUrls);
                } else {
                    SwingUtilities.invokeLater(() -> {
                        outputArea.append("没有发现外部JavaScript文件\n");
                        finishDiscovery();
                    });
                }

            } else {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("未收到目标响应\n"));
                finishDiscovery();
            }

        } catch (Exception e) {
            SwingUtilities.invokeLater(() ->
                outputArea.append("API发现过程中发生错误: " + e.getMessage() + "\n"));
            callbacks.printError("API discovery error: " + e.getMessage());
            finishDiscovery();
        }
    }

    /**
     * 开始递归分析单个JavaScript文件
     */
    private void startRecursiveJsAnalysis(String jsUrl) {
        // 使用队列来管理待分析的JS文件
        Set<String> processedUrls = new HashSet<>();
        Queue<String> pendingUrls = new LinkedList<>();

        pendingUrls.add(jsUrl);
        jsUrls.add(jsUrl);

        // 在后台线程中递归处理
        new Thread(() -> {
            processJsUrlsRecursively(pendingUrls, processedUrls);
        }).start();
    }

    /**
     * 开始递归分析多个JavaScript文件
     */
    private void startRecursiveJsAnalysisForMultiple(Set<String> jsUrls) {
        // 使用队列来管理待分析的JS文件
        Set<String> processedUrls = new HashSet<>();
        Queue<String> pendingUrls = new LinkedList<>(jsUrls);

        this.jsUrls.addAll(jsUrls);

        // 在后台线程中递归处理
        new Thread(() -> {
            processJsUrlsRecursively(pendingUrls, processedUrls);
        }).start();
    }

    /**
     * 递归处理JavaScript文件队列（增强循环检测）
     */
    private void processJsUrlsRecursively(Queue<String> pendingUrls, Set<String> processedUrls) {
        final int[] counters = {0, 1}; // [totalProcessed, currentRound]
        final int MAX_JS_FILES = 1000; // 最大处理文件数限制
        final int MAX_ROUNDS = 50; // 最大递归轮次限制

        SwingUtilities.invokeLater(() ->
            outputArea.append("  [安全检查] 启用循环检测保护机制\n"));

        while (!pendingUrls.isEmpty()) {
            // 安全检查1: 防止处理过多文件
            if (counters[0] >= MAX_JS_FILES) {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("  [安全中断] 达到最大文件处理数限制 (" + MAX_JS_FILES + ")，停止递归分析\n"));
                break;
            }

            // 安全检查2: 防止过多递归轮次
            if (counters[1] > MAX_ROUNDS) {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("  [安全中断] 达到最大递归轮次限制 (" + MAX_ROUNDS + ")，停止递归分析\n"));
                break;
            }

            String currentJsUrl = pendingUrls.poll();

            // 循环检测1: 避免重复处理同一文件
            if (processedUrls.contains(currentJsUrl)) {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("  [循环检测] 跳过已处理文件: " + currentJsUrl + "\n"));
                continue;
            }

            processedUrls.add(currentJsUrl);
            counters[0]++; // totalProcessed

            final int currentCount = counters[0];
            final int round = counters[1];

            SwingUtilities.invokeLater(() ->
                outputArea.append("  [Round " + round + "] 分析JS文件 " + currentCount + "/" + MAX_JS_FILES + ": " + currentJsUrl + "\n"));

            try {
                // 分析当前JavaScript文件
                String jsContent = downloadAndAnalyzeJsFile(currentJsUrl);

                if (jsContent != null) {
                    // 从JS内容中查找新的JavaScript文件
                    Set<String> newJsUrls = findDynamicJavaScriptUrls(jsContent, currentJsUrl);

                    // 记录依赖关系
                    jsDependencyGraph.put(currentJsUrl, new HashSet<>(newJsUrls));

                    if (!newJsUrls.isEmpty()) {
                        SwingUtilities.invokeLater(() ->
                            outputArea.append("    -> 发现 " + newJsUrls.size() + " 个新的JS文件\n"));

                        int addedCount = 0;
                        int skippedCount = 0;
                        int cycleSkippedCount = 0;

                        // 将新发现的JS文件添加到队列中
                        for (String newJsUrl : newJsUrls) {
                            // 循环检测2: 检查是否已处理或在队列中
                            if (processedUrls.contains(newJsUrl)) {
                                skippedCount++;
                                SwingUtilities.invokeLater(() ->
                                    outputArea.append("      [已处理] " + newJsUrl + "\n"));
                            } else if (pendingUrls.contains(newJsUrl)) {
                                skippedCount++;
                                SwingUtilities.invokeLater(() ->
                                    outputArea.append("      [队列中] " + newJsUrl + "\n"));
                            } else {
                                // 循环检测3: 检查是否与当前文件形成直接循环
                                if (newJsUrl.equals(currentJsUrl)) {
                                    SwingUtilities.invokeLater(() ->
                                        outputArea.append("      [循环检测] 跳过自引用: " + newJsUrl + "\n"));
                                    cycleSkippedCount++;
                                    continue;
                                }

                                // 循环检测4: 检查是否会形成复杂循环依赖
                                if (wouldCreateCycle(newJsUrl, currentJsUrl)) {
                                    SwingUtilities.invokeLater(() ->
                                        outputArea.append("      [循环检测] 跳过循环依赖: " + newJsUrl + " -> " + currentJsUrl + "\n"));
                                    cycleSkippedCount++;
                                    continue;
                                }

                                pendingUrls.add(newJsUrl);
                                jsUrls.add(newJsUrl);
                                addedCount++;

                                SwingUtilities.invokeLater(() ->
                                    outputArea.append("      + " + newJsUrl + "\n"));
                            }
                        }

                        if (skippedCount > 0 || cycleSkippedCount > 0) {
                            final int finalSkippedCount = skippedCount;
                            final int finalAddedCount = addedCount;
                            final int finalCycleSkippedCount = cycleSkippedCount;
                            SwingUtilities.invokeLater(() ->
                                outputArea.append("    -> 新增 " + finalAddedCount + " 个文件，跳过 " + finalSkippedCount + " 个重复文件，" + finalCycleSkippedCount + " 个循环依赖\n"));
                        }
                    }
                }

            } catch (Exception e) {
                SwingUtilities.invokeLater(() ->
                    outputArea.append("    -> 分析失败: " + e.getMessage() + "\n"));
            }

            // 如果当前轮次的所有文件都处理完了，开始下一轮
            if (pendingUrls.isEmpty() ||
                pendingUrls.stream().allMatch(processedUrls::contains)) {
                counters[1]++; // currentRound

                if (!pendingUrls.isEmpty()) {
                    SwingUtilities.invokeLater(() ->
                        outputArea.append("  [Round " + counters[1] + "] 开始新一轮递归分析...\n"));
                }
            }
        }

        // 所有JavaScript文件分析完成
        SwingUtilities.invokeLater(() -> {
            outputArea.append("\n递归分析完成！\n");
            outputArea.append("总共分析了 " + counters[0] + " 个JavaScript文件\n");
            outputArea.append("发现API路径总数: " + apiPaths.size() + "\n");

            finishDiscoveryRecursive();
        });
    }

    /**
     * 下载并分析JavaScript文件
     */
    private String downloadAndAnalyzeJsFile(String jsUrl) {
        try {
            URL url = new URL(jsUrl);
            IHttpService httpService = helpers.buildHttpService(url.getHost(), getEffectivePort(url), url.getProtocol());

            String path = url.getPath();
            if (path == null || path.isEmpty()) {
                path = "/";
            }

            String requestLine = "GET " + path +
                (url.getQuery() != null ? "?" + url.getQuery() : "") + " HTTP/1.1\r\n";
            String headers = buildHttpHeaders(url.getHost(),
                                            "Mozilla/5.0 (API Security Checker)",
                                            "application/javascript, */*");
            headers += "\r\n";
            byte[] request = (requestLine + headers).getBytes();

            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, request);

            if (requestResponse.getResponse() != null) {
                String jsContent = new String(requestResponse.getResponse());

                // 提取响应体（跳过HTTP头部）
                int bodyStart = jsContent.indexOf("\r\n\r\n");
                if (bodyStart != -1) {
                    jsContent = jsContent.substring(bodyStart + 4);
                }

                // 分析JavaScript内容查找API路径
                findApiPaths(jsContent, jsUrl);

                // 如果启用了同步分析，提取敏感信息
                if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                    extractSensitiveInfo(jsContent, jsUrl);
                }

                return jsContent;
            }

        } catch (Exception e) {
            SwingUtilities.invokeLater(() ->
                outputArea.append("    -> 下载JS文件失败: " + jsUrl + " - " + e.getMessage() + "\n"));
        }

        return null;
    }

    /**
     * 完成递归发现过程
     */
    private void finishDiscoveryRecursive() {
        SwingUtilities.invokeLater(() -> {
            outputArea.append("\n=== 递归API发现完成 ===\n");
            outputArea.append("发现API路径总数: " + apiPaths.size() + "\n");
            outputArea.append("分析JS文件总数: " + jsUrls.size() + "\n");

            if (!apiPaths.isEmpty()) {
                outputArea.append("\n发现的API路径:\n");
                for (String apiPath : apiPaths) {
                    outputArea.append("  - " + apiPath + "\n");
                }
            }

            outputArea.append("\n提示: 发现的内容已自动同步到其他Tab\n\n");

            // 如果启用了同步分析，显示敏感信息结果
            if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                outputArea.append("同步敏感信息分析已完成，请查看敏感信息提取Tab页查看详细结果\n\n");
            }
        });

        // 更新结果表格
        updateResultsTable();

        // 同步到其他Tab
        syncToOtherTabs();
    }
    
    /**
     * 清除结果
     */
    private void clearResults() {
        int result = JOptionPane.showConfirmDialog(mainPanel, 
            "确定要清除所有发现结果吗？", "确认清除", 
            JOptionPane.YES_NO_OPTION);
        
        if (result == JOptionPane.YES_OPTION) {
            discoveryResults.clear();
            tableModel.setRowCount(0);
            outputArea.setText("API发现日志将在这里显示...\n");
        }
    }
    
    /**
     * 导出结果
     */
    private void exportResults() {
        if (apiPaths.isEmpty() && sensitiveData.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "没有可导出的数据", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出API发现结果");
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        fileChooser.setSelectedFile(new java.io.File("api_discovery_results.txt"));

        int result = fileChooser.showSaveDialog(mainPanel);
        if (result == JFileChooser.APPROVE_OPTION) {
            java.io.File file = fileChooser.getSelectedFile();

            try (java.io.PrintWriter writer = new java.io.PrintWriter(
                    new java.io.OutputStreamWriter(
                        new java.io.FileOutputStream(file),
                        java.nio.charset.StandardCharsets.UTF_8))) {

                // 写入头部信息
                writer.println("=== API发现结果导出 ===");
                writer.println("导出时间: " + java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                writer.println("目标URL: " + urlField.getText().trim());
                writer.println();

                // 导出API路径
                writer.println("=== 发现的API路径 (" + apiPaths.size() + " 个) ===");
                if (!apiPaths.isEmpty()) {
                    int index = 1;
                    for (String apiPath : apiPaths) {
                        writer.println(index + ". " + apiPath);
                        index++;
                    }
                } else {
                    writer.println("无");
                }
                writer.println();

                // 导出敏感信息
                writer.println("=== 发现的敏感信息 ===");
                if (!sensitiveData.isEmpty()) {
                    for (Map.Entry<String, List<String>> entry : sensitiveData.entrySet()) {
                        String type = entry.getKey();
                        List<String> dataList = entry.getValue();

                        writer.println("类型: " + type + " (" + dataList.size() + " 个)");
                        for (String data : dataList) {
                            String source = sensitiveDataSources.getOrDefault(data, "未知");
                            writer.println("  - " + data + " (来源: " + source + ")");
                        }
                        writer.println();
                    }
                } else {
                    writer.println("无");
                }

                // 导出统计信息
                writer.println("=== 统计信息 ===");
                writer.println("分析的JavaScript文件数: " + jsUrls.size());
                writer.println("发现的API路径数: " + apiPaths.size());
                writer.println("发现的敏感信息数: " + sensitiveData.values().stream()
                    .mapToInt(List::size).sum());

                JOptionPane.showMessageDialog(mainPanel,
                    "导出成功！\n文件保存至: " + file.getAbsolutePath(),
                    "导出完成", JOptionPane.INFORMATION_MESSAGE);

                // 在日志中记录导出信息
                SwingUtilities.invokeLater(() ->
                    outputArea.append("结果已导出到: " + file.getAbsolutePath() + "\n"));

            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel,
                    "导出失败: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);

                SwingUtilities.invokeLater(() ->
                    outputArea.append("导出失败: " + e.getMessage() + "\n"));
            }
        }
    }
    
    /**
     * 同步到其他Tab
     */
    private void syncToOtherTabs() {
        if (mainTab == null) {
            JOptionPane.showMessageDialog(mainPanel, "无法访问主Tab，同步失败", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        final int[] counters = {0, 0}; // [syncedPaths, syncedSensitive]

        try {
            // 同步API路径到路径发现Tab
            for (String apiPath : apiPaths) {
                String host = "未知主机";
                String path = apiPath;

                // 优先从API路径本身提取主机信息（如果是完整URL）
                if (apiPath.startsWith("http://") || apiPath.startsWith("https://")) {
                    try {
                        URL url = new URL(apiPath);
                        host = url.getHost();
                        if (url.getPort() != -1 && url.getPort() != 80 && url.getPort() != 443) {
                            host += ":" + url.getPort();
                        }
                        path = url.getPath();
                    } catch (Exception e) {
                        // 如果解析失败，使用原始路径
                    }
                } else {
                    // 如果API路径是相对路径，从来源URL提取主机信息
                    String sourceUrl = apiPathSources.get(apiPath);
                    if (sourceUrl != null) {
                        host = extractHost(sourceUrl);
                    }
                }

                // 创建RouteContent并添加到主Tab
                RouteContent route = new RouteContent(host, path, "API发现Tab", "API发现");
                route.addMethod("GET"); // 默认方法
                mainTab.addRoute(route);
                counters[0]++; // syncedPaths
            }

            // 同步敏感信息到敏感信息Tab
            for (Map.Entry<String, List<String>> entry : sensitiveData.entrySet()) {
                String type = entry.getKey();
                List<String> dataList = entry.getValue();

                for (String data : dataList) {
                    String source = sensitiveDataSources.getOrDefault(data, "API发现Tab");

                    // 创建SensitiveDataItem并添加到主Tab
                    EnhancedSensitiveInfoExtractor.SensitiveDataItem sensitiveItem =
                        new EnhancedSensitiveInfoExtractor.SensitiveDataItem(
                            type, data, source, source, "通过API发现Tab提取");
                    mainTab.addSensitiveData(sensitiveItem);
                    counters[1]++; // syncedSensitive
                }
            }

            // 显示同步结果
            String message = String.format("同步完成！\n已同步 %d 个API路径\n已同步 %d 个敏感信息",
                                          counters[0], counters[1]);
            JOptionPane.showMessageDialog(mainPanel, message, "同步成功", JOptionPane.INFORMATION_MESSAGE);

            // 在日志中记录同步信息
            SwingUtilities.invokeLater(() -> {
                outputArea.append("\n=== 数据同步完成 ===\n");
                outputArea.append("同步API路径: " + counters[0] + " 个\n");
                outputArea.append("同步敏感信息: " + counters[1] + " 个\n");
                outputArea.append("目标: 路径发现Tab、敏感信息Tab\n\n");
            });

        } catch (Exception e) {
            String errorMsg = "同步过程中发生错误: " + e.getMessage();
            JOptionPane.showMessageDialog(mainPanel, errorMsg, "同步失败", JOptionPane.ERROR_MESSAGE);

            SwingUtilities.invokeLater(() ->
                outputArea.append("同步错误: " + e.getMessage() + "\n"));
        }
    }
    
    /**
     * 完成发现过程
     */
    private void finishDiscovery() {
        outputArea.append("\nAPI发现完成！\n");
        outputArea.append("发现API路径总数: " + apiPaths.size() + "\n");
        outputArea.append("分析JS文件总数: " + jsUrls.size() + "\n");
        outputArea.append("提示: 发现的内容已自动同步到其他Tab\n\n");

        // 如果启用了同步分析，显示敏感信息结果
        if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
            outputArea.append("同步敏感信息分析已完成，请查看敏感信息提取Tab页查看详细结果\n\n");
        }

        // 更新结果表格
        updateResultsTable();

        // 同步到其他Tab
        syncToOtherTabs();
    }

    /**
     * 更新结果表格
     */
    private void updateResultsTable() {
        tableModel.setRowCount(0);

        // 添加API路径结果
        for (String apiPath : apiPaths) {
            tableModel.addRow(new Object[]{"API路径", apiPath, "JavaScript分析", "✅ 已发现"});
        }

        // 添加敏感信息结果
        for (Map.Entry<String, List<String>> entry : sensitiveData.entrySet()) {
            String type = entry.getKey();
            for (String data : entry.getValue()) {
                String source = sensitiveDataSources.getOrDefault(data, "未知");
                tableModel.addRow(new Object[]{"敏感信息 (" + type + ")", data, source, "✅ 已提取"});
            }
        }
    }

    /**
     * 获取有效端口
     */
    private int getEffectivePort(URL url) {
        int port = url.getPort();
        if (port == -1) {
            if ("https".equals(url.getProtocol())) {
                return 443;
            } else {
                return 80;
            }
        }
        return port;
    }

    /**
     * 构建HTTP头部
     */
    private String buildHttpHeaders(String host, String userAgent, String accept) {
        StringBuilder headers = new StringBuilder();
        headers.append("Host: ").append(host).append("\r\n");
        headers.append("User-Agent: ").append(userAgent).append("\r\n");
        headers.append("Accept: ").append(accept).append("\r\n");
        headers.append("Accept-Language: en-US,en;q=0.9\r\n");
        headers.append("Accept-Encoding: gzip, deflate\r\n");
        headers.append("Connection: close\r\n");

        // 添加自定义头部
        String customHeaders = customHeadersField.getText().trim();
        if (!customHeaders.isEmpty()) {
            String[] headerLines = customHeaders.split("\n");
            for (String headerLine : headerLines) {
                headerLine = headerLine.trim();
                if (!headerLine.isEmpty() && headerLine.contains(":")) {
                    headers.append(headerLine).append("\r\n");
                }
            }
        }

        return headers.toString();
    }

    /**
     * 查找JavaScript文件URL
     */
    private Set<String> findJavaScriptUrls(String html, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();

        // Simple regex to find script src attributes
        Pattern scriptPattern = Pattern.compile("<script[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = scriptPattern.matcher(html);

        while (matcher.find()) {
            String src = matcher.group(1);
            if (src.endsWith(".js")) {
                try {
                    if (src.startsWith("http")) {
                        jsUrls.add(src);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, src);
                        jsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        }

        return jsUrls;
    }

    /**
     * 分析内联JavaScript
     */
    private void analyzeInlineJavaScript(String htmlContent, String sourceUrl) {
        try {
            // Extract inline JavaScript from script tags
            Pattern inlineScriptPattern = Pattern.compile("<script[^>]*>([\\s\\S]*?)</script>", Pattern.CASE_INSENSITIVE);
            Matcher scriptMatcher = inlineScriptPattern.matcher(htmlContent);

            int inlineScriptCount = 0;
            while (scriptMatcher.find()) {
                String scriptContent = scriptMatcher.group(1);
                if (scriptContent != null && !scriptContent.trim().isEmpty()) {
                    inlineScriptCount++;

                    // Look for API paths in inline script
                    findApiPaths(scriptContent, sourceUrl + "#inline-script-" + inlineScriptCount);

                    // 如果启用了同步分析，提取内联脚本的敏感信息
                    if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                        extractSensitiveInfo(scriptContent, sourceUrl + "#inline-script-" + inlineScriptCount);
                    }

                    // Look for dynamically loaded JS files in inline scripts
                    Set<String> dynamicJsUrls = findDynamicJavaScriptUrls(scriptContent, sourceUrl);
                    for (String dynamicJsUrl : dynamicJsUrls) {
                        jsUrls.add(dynamicJsUrl);
                        // Analyze dynamic JS files in separate thread
                        new Thread(() -> analyzeJavaScriptFile(dynamicJsUrl)).start();
                    }
                }
            }

            final int finalCount = inlineScriptCount;
            SwingUtilities.invokeLater(() -> {
                outputArea.append("Analyzed " + finalCount + " inline script blocks\n");
                if (finalCount > 0) {
                    outputArea.append("Found potential API endpoints in inline JavaScript\n");
                }
            });

        } catch (Exception e) {
            callbacks.printError("Error analyzing inline JavaScript: " + e.getMessage());
        }
    }

    /**
     * 查找API路径 - 核心提取逻辑
     */
    private void findApiPaths(String content, String sourceUrl) {
        // Enhanced API path patterns based on Chrome extension rules
        String[] patterns = {
            // === 基础API路径模式 (原有) ===
            "[\"\\']/[a-zA-Z0-9_/\\-]*api[a-zA-Z0-9_/\\-]*[\"\\'\\s]",
            "[\"\\']/[a-zA-Z0-9_/\\-]*[^a-zA-Z0-9_/\\-][a-zA-Z0-9_/\\-]*[\"\\'\\s]",
            "url\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",
            "path\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",
            "endpoint\\s*[=:]\\s*[\"\\']/[a-zA-Z0-9_/\\-]+[\"\\'\\s]",

            // === 增强的API路径模式 (参考Chrome扩展) ===
            // 1. 完整路径模式 - 匹配 '/path' 格式 (对应Chrome的path规则)
            "[\"\\''](?:/|\\.\\./|\\./)[^/\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\]([^\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\])*?[\"\\'']",

            // 2. 不完整路径模式 - 匹配 'path/subpath' 格式 (对应Chrome的incomplete_path规则)
            "[\"\\''][^/\\>\\< \\)\\(\\{\\}\\,\\'\\\"\\\\][\\w/]*?/[\\w/]*?[\"\\'']",

            // 3. HTTP请求方法相关模式
            "fetch\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "axios\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "\\$\\.(?:get|post|put|delete|ajax)\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "XMLHttpRequest.*?open\\s*\\([^,]*,\\s*[\"\\'']([^\"']+)[\"\\'']",

            // 4. 配置相关模式
            "baseURL\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
            "apiUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
            "restUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",
            "serviceUrl\\s*[=:]\\s*[\"\\'']([^\"']+)[\"\\'']",

            // 5. 现代前端框架模式
            "this\\.[\\$]?http\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "http\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",
            "httpClient\\.[a-zA-Z]+\\s*\\(\\s*[\"\\'']([^\"']+)[\"\\'']",

            // 6. 特定关键词API模式
            "[\"\\']/[a-zA-Z0-9_/\\-]*(?:api|v\\d+|rest|service|ajax|json|gateway|admin|user|auth|login|register|upload|download)[a-zA-Z0-9_/\\-]*[\"\\'\\s]",

            // 7. 路由配置模式
            "(?i)(?<=path:)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=path\\s:)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=path=)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=path\\s=)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=url:)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=url\\s:)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=url=)\\s?[\"\\'']([^\"']+)[\"\\'']",
            "(?i)(?<=url\\s=)\\s?[\"\\'']([^\"']+)[\"\\'']",

            // 8. href和action属性
            "(href|action).{0,3}=.{0,3}[\"\\'']([^\\s\\'\"\\>\\<\\)\\(]{2,250})[\"\\'']",
            "(href|action).{0,3}=.{0,3}([^\\s\\'\"\\>\\<\\)\\(]{2,250})",

            // 9. 通用路径捕获 (包装在引号中的路径)
            "(?:\"|'|`)(/[^\"'`<>]+)(?:\"|'|`)",

            // 10. 排除汉字的路径模式
            "[\"\\']/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?[\"\\'']",
            "[\"\\''][^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?/[^\\s\\'\"\\>\\<\\:\\)\\(\\u4e00-\\u9fa5]{1,250}?[\"\\'']",

            // 11. HTTP完整URL模式
            "[\"\\'']http[^\\s\\'\"\\>\\<\\)\\(]{2,250}?[\"\\'']",
            "=http[^\\s\\'\"\\>\\<\\)\\(]{2,250}",

            // 12. REST API常见端点模式
            "[\"\\']/[a-zA-Z0-9_/\\-]*(?:users|user|auth|login|register|profile|admin|dashboard|data|list|create|update|delete|search|upload|download|api|v1|v2|v3|rest|service|gateway)[a-zA-Z0-9_/\\-]*[\"\\'\\s]"
        };

        Set<String> foundPaths = new HashSet<>();

        for (String patternStr : patterns) {
            try {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE | Pattern.MULTILINE);
                Matcher matcher = pattern.matcher(content);

                while (matcher.find()) {
                    String path = null;

                    // 尝试从捕获组获取路径
                    if (matcher.groupCount() > 0 && matcher.group(1) != null) {
                        path = matcher.group(1);
                    } else if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                        path = matcher.group(2);
                    } else {
                        // 从完整匹配中提取路径
                        String match = matcher.group();
                        path = extractPathFromMatch(match);
                    }

                    if (path != null) {
                        path = cleanPath(path);
                        if (isValidPath(path)) {
                            foundPaths.add(path);
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略正则表达式错误，继续处理下一个模式
                callbacks.printError("Pattern error for: " + patternStr + " - " + e.getMessage());
            }
        }

        // 将找到的路径添加到全局集合，并记录来源
        for (String path : foundPaths) {
            apiPaths.add(path);
            apiPathSources.put(path, sourceUrl); // 记录API路径的来源URL
        }

        // 向新的API扫描Tab发送路径信息
        if (mainTab != null && !foundPaths.isEmpty()) {
            String host = extractHost(sourceUrl);
            for (String path : foundPaths) {
                RouteContent route = new RouteContent(host, path, sourceUrl, "JavaScript分析");
                route.addMethod("GET"); // 默认方法，可能需要进一步分析确定
                mainTab.addRoute(route);
            }
        }

        // 输出调试信息
        if (!foundPaths.isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                outputArea.append("  [API Paths] Found " + foundPaths.size() + " paths in " + sourceUrl + "\n");
                for (String path : foundPaths) {
                    outputArea.append("    -> " + path + "\n");
                }

                // 添加同步信息
                if (mainTab != null) {
                    outputArea.append("  [Sync] Paths synchronized to API Scan Tab\n");
                }
            });
        }
    }

    /**
     * 从匹配字符串中提取路径
     */
    private String extractPathFromMatch(String match) {
        // 移除常见的包装字符
        match = match.replaceAll("^[\"'`=\\s]+|[\"'`\\s]+$", "");
        match = match.replaceAll("^(href|action)\\s*=\\s*", "");

        // 查找路径模式
        Pattern[] pathPatterns = {
            Pattern.compile("[\"\\'`]([^\"'`<>\\s]+)[\"\\'`]"),
            Pattern.compile("=\\s*([^\\s\"'<>]+)"),
            Pattern.compile("(/[^\\s\"'<>]+)"),
            Pattern.compile("(http[^\\s\"'<>]+)")
        };

        for (Pattern p : pathPatterns) {
            Matcher m = p.matcher(match);
            if (m.find()) {
                return m.group(1);
            }
        }

        // 如果没有找到特定模式，返回清理后的匹配
        if (match.startsWith("/") || match.startsWith("http")) {
            return match;
        }

        return null;
    }

    /**
     * 清理和标准化路径
     */
    private String cleanPath(String path) {
        if (path == null) return null;

        // 移除引号和空白字符
        path = path.replaceAll("^[\"'`\\s]+|[\"'`\\s]+$", "");

        // URL解码
        path = path.replace("%3A", ":");
        path = path.replace("%2F", "/");
        path = path.replace("\\/", "/");
        path = path.replace("\\\\", "");

        // 移除反斜杠
        if (path.endsWith("\\")) {
            path = path.substring(0, path.length() - 1);
        }

        // 移除等号前缀
        if (path.startsWith("=")) {
            path = path.substring(1);
        }

        // 移除href前缀
        if (path.startsWith("href=")) {
            path = path.substring(5);
        }

        // 只移除片段标识符，保留查询参数（因为查询参数可能包含有用的API端点信息）
        int fragmentIndex = path.indexOf('#');
        if (fragmentIndex != -1) {
            path = path.substring(0, fragmentIndex);
        }

        // 标准化路径分隔符
        path = path.replace("\\", "/");

        return path.trim();
    }

    /**
     * 验证路径是否有效
     */
    private boolean isValidPath(String path) {
        if (path == null || path.isEmpty() || path.equals("href")) {
            return false;
        }

        // 检查最小长度
        if (path.length() < 2) {
            return false;
        }

        // 排除静态资源
        if (isStaticResource(path)) {
            return false;
        }

        // 排除明显的非路径内容
        String[] blacklist = {
            "javascript:", "data:", "mailto:", "tel:", "ftp:",
            "{{", "}}", "<%", "%>", "<?", "?>",
            "console.log", "alert(", "document.",
            "window.", "return", "function", "var ", "let ", "const "
        };

        String lowerPath = path.toLowerCase();
        for (String item : blacklist) {
            if (lowerPath.contains(item)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 从URL中提取主机信息
     */
    private String extractHost(String url) {
        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                URL urlObj = new URL(url);
                String host = urlObj.getHost();
                int port = urlObj.getPort();

                if (port != -1 && port != 80 && port != 443) {
                    return host + ":" + port;
                }
                return host;
            }
            return "本地";
        } catch (Exception e) {
            return "未知主机";
        }
    }

    /**
     * 检查是否为静态资源
     */
    private boolean isStaticResource(String path) {
        if (path == null) return false;

        String lowerPath = path.toLowerCase();
        String[] staticExtensions = {
            ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
            ".woff", ".woff2", ".ttf", ".eot", ".mp4", ".mp3", ".pdf",
            ".zip", ".rar", ".tar", ".gz", ".xml", ".txt", ".json"
        };

        for (String ext : staticExtensions) {
            if (lowerPath.endsWith(ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 提取敏感信息（完整实现）
     */
    private void extractSensitiveInfo(String content, String source) {
        if (content == null || content.isEmpty()) return;

        // 使用完整的敏感信息模式进行匹配
        for (int i = 0; i < SENSITIVE_PATTERNS.length; i++) {
            Pattern pattern = SENSITIVE_PATTERNS[i];
            String typeName = SENSITIVE_TYPES[i];

            try {
                Matcher matcher = pattern.matcher(content);
                while (matcher.find()) {
                    String rawValue = null;

                    // 根据不同的模式提取敏感信息
                    if (matcher.groupCount() > 0) {
                        // 对于有捕获组的模式，使用第一个捕获组
                        rawValue = matcher.group(1);
                    } else {
                        // 对于没有捕获组的模式，使用完整匹配
                        rawValue = matcher.group();
                    }

                    // 清理敏感信息值
                    if (rawValue != null) {
                        final String sensitiveValue = cleanSensitiveValue(rawValue);

                        // 验证敏感信息的有效性
                        if (isValidSensitiveValue(sensitiveValue, typeName)) {
                            // 存储敏感信息
                            if (!sensitiveData.containsKey(typeName)) {
                                sensitiveData.put(typeName, new ArrayList<>());
                            }

                            // 改进的去重逻辑：检查相同内容和来源的敏感信息
                            if (!isDuplicateSensitiveInfo(typeName, sensitiveValue, source)) {
                                sensitiveData.get(typeName).add(sensitiveValue);
                                sensitiveDataSources.put(sensitiveValue, source);

                                // 输出发现的敏感信息日志
                                SwingUtilities.invokeLater(() ->
                                    outputArea.append("  [敏感信息] 发现 " + typeName + ": " +
                                                    maskSensitiveValue(sensitiveValue) + " (来源: " + source + ")\n"));
                            } else {
                                // 输出去重信息（调试用）
                                SwingUtilities.invokeLater(() ->
                                    outputArea.append("  [去重] 跳过重复敏感信息 " + typeName + ": " +
                                                    maskSensitiveValue(sensitiveValue) + " (来源: " + source + ")\n"));
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // 忽略正则表达式错误，继续处理下一个模式
                callbacks.printError("Sensitive info extraction error for pattern " + i + ": " + e.getMessage());
            }
        }
    }

    /**
     * 清理敏感信息值
     */
    private String cleanSensitiveValue(String value) {
        if (value == null) return null;

        // 移除引号和空白字符
        value = value.replaceAll("^[\"'`\\s]+|[\"'`\\s]+$", "");

        // 移除常见的前缀
        value = value.replaceAll("^(Bearer\\s+|Basic\\s+|Token\\s+)", "");

        return value.trim();
    }

    /**
     * 验证敏感信息值的有效性
     */
    private boolean isValidSensitiveValue(String value, String type) {
        if (value == null || value.isEmpty()) {
            return false;
        }

        // 检查最小长度
        if (value.length() < 3) {
            return false;
        }

        // 排除明显的无效值（使用精确匹配，避免误杀）
        String[] exactInvalidValues = {
            "null", "undefined", "true", "false", "test", "demo",
            "example", "placeholder", "your_key_here", "your_token_here"
        };

        // 排除明显的占位符模式
        String[] patternInvalidValues = {
            "xxxxxxxx", "********", "{{", "}}", "<%", "%>", "<?", "?>"
        };

        String lowerValue = value.toLowerCase();

        // 精确匹配检查
        for (String invalid : exactInvalidValues) {
            if (lowerValue.equals(invalid)) {
                return false;
            }
        }

        // 模式匹配检查
        for (String invalid : patternInvalidValues) {
            if (lowerValue.contains(invalid)) {
                return false;
            }
        }

        // 特殊情况：单个数字或字符（但允许作为复杂字符串的一部分）
        if (value.length() <= 2 && (value.equals("0") || value.equals("1"))) {
            return false;
        }

        // 特定类型的额外验证
        switch (type) {
            case "手机号":
                return value.matches("1[3-9]\\d{9}");
            case "邮箱地址":
                return value.contains("@") && value.contains(".");
            case "IP地址":
                return isValidIP(value);
            case "JWT Token":
                return value.contains(".") && value.length() > 20;
            default:
                return true;
        }
    }

    /**
     * 验证IP地址格式
     */
    private boolean isValidIP(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length != 4) return false;

            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查是否为重复的敏感信息
     * 基于类型、内容和来源进行去重判断
     */
    private boolean isDuplicateSensitiveInfo(String typeName, String sensitiveValue, String source) {
        // 创建唯一标识符：类型:值:来源
        String uniqueKey = typeName + ":" + sensitiveValue + ":" + source;

        // 检查是否已经处理过这个敏感信息
        if (processedSensitiveInfo.contains(uniqueKey)) {
            return true;
        }

        // 添加到已处理集合中
        processedSensitiveInfo.add(uniqueKey);
        return false;
    }

    /**
     * 掩码敏感信息值用于日志显示
     */
    private String maskSensitiveValue(String value) {
        if (value == null || value.length() <= 6) {
            return "***";
        }

        // 显示前3个和后3个字符，中间用*代替
        return value.substring(0, 3) + "***" + value.substring(value.length() - 3);
    }

    /**
     * 查找动态JavaScript URL（完整实现）
     */
    private Set<String> findDynamicJavaScriptUrls(String jsContent, String baseUrl) {
        Set<String> dynamicJsUrls = new HashSet<>();

        // Pattern 1: Advanced webpack chunk mapping analysis
        // First, try to find the path template in the code
        String pathTemplate = extractWebpackPathTemplate(jsContent);

        if (pathTemplate != null) {
            // Found explicit path template, use it for chunk mapping
            Pattern chunkPattern = Pattern.compile("\"(chunk-[a-zA-Z0-9\\-]+)\"\\s*:\\s*\"([a-zA-Z0-9]+)\"", Pattern.CASE_INSENSITIVE);
            Matcher chunkMatcher = chunkPattern.matcher(jsContent);

            while (chunkMatcher.find()) {
                String chunkName = chunkMatcher.group(1);
                String filename = chunkMatcher.group(2);

                try {
                    URL base = new URL(baseUrl);
                    // Use the discovered path template
                    String jsPath = pathTemplate.replace("{chunk}", chunkName).replace("{hash}", filename);
                    URL jsUrl = new URL(base, jsPath);
                    dynamicJsUrls.add(jsUrl.toString());

                    SwingUtilities.invokeLater(() ->
                        outputArea.append("  [Webpack] Found chunk: " + jsPath + "\n"));
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        } else {
            // Fallback: use basic chunk mapping for simple cases
            Pattern chunkPattern = Pattern.compile("\"(chunk-[a-zA-Z0-9\\-]+)\"\\s*:\\s*\"([a-zA-Z0-9]+)\"", Pattern.CASE_INSENSITIVE);
            Matcher chunkMatcher = chunkPattern.matcher(jsContent);

            while (chunkMatcher.find()) {
                String chunkName = chunkMatcher.group(1);
                String filename = chunkMatcher.group(2);

                try {
                    URL base = new URL(baseUrl);
                    // Only try the most common patterns when no template is found
                    String[] possiblePaths = {
                        "/static/js/" + chunkName + "." + filename + ".js",
                        "/js/" + chunkName + "." + filename + ".js",
                        "/assets/js/" + chunkName + "." + filename + ".js"
                    };

                    for (String path : possiblePaths) {
                        URL jsUrl = new URL(base, path);
                        dynamicJsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        }

        // Pattern 2: Dynamic imports like import('./chunk-xxx.js')
        Pattern dynamicImportPattern = Pattern.compile("import\\s*\\(\\s*[\"']([^\"']+\\.js)[\"']\\s*\\)", Pattern.CASE_INSENSITIVE);
        Matcher importMatcher = dynamicImportPattern.matcher(jsContent);

        while (importMatcher.find()) {
            String jsPath = importMatcher.group(1);
            try {
                if (jsPath.startsWith("http")) {
                    dynamicJsUrls.add(jsPath);
                } else {
                    URL base = new URL(baseUrl);
                    URL jsUrl = new URL(base, jsPath);
                    dynamicJsUrls.add(jsUrl.toString());
                }
            } catch (Exception e) {
                // Skip invalid URLs
            }
        }

        // Pattern 3: RequireJS/AMD module loading
        Pattern requirePattern = Pattern.compile("require\\s*\\(\\s*\\[\\s*[\"']([^\"']+\\.js)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher requireMatcher = requirePattern.matcher(jsContent);

        while (requireMatcher.find()) {
            String jsPath = requireMatcher.group(1);
            try {
                if (jsPath.startsWith("http")) {
                    dynamicJsUrls.add(jsPath);
                } else {
                    URL base = new URL(baseUrl);
                    URL jsUrl = new URL(base, jsPath);
                    dynamicJsUrls.add(jsUrl.toString());
                }
            } catch (Exception e) {
                // Skip invalid URLs
            }
        }

        // Pattern 4: Generic .js file references in strings
        Pattern jsFilePattern = Pattern.compile("[\"']([^\"']*\\.js)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher jsFileMatcher = jsFilePattern.matcher(jsContent);

        while (jsFileMatcher.find()) {
            String jsPath = jsFileMatcher.group(1);
            // Only consider paths that look like actual JS files (contain / or start with reasonable names)
            if (jsPath.contains("/") || jsPath.matches("^[a-zA-Z][a-zA-Z0-9\\-_]*\\.js$")) {
                try {
                    if (jsPath.startsWith("http")) {
                        dynamicJsUrls.add(jsPath);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, jsPath);
                        dynamicJsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // Skip invalid URLs
                }
            }
        }

        return dynamicJsUrls;
    }

    /**
     * 提取Webpack路径模板
     */
    private String extractWebpackPathTemplate(String jsContent) {
        // Pattern for the exact format from your example:
        // function d(c){return f.p+"static/js/"+({...}[c]||c)+"."+{"chunk-01214770":"1b1785dd",...}
        Pattern webpackFunctionPattern = Pattern.compile(
            "return\\s+[^+\"']*\\+\\s*[\"']([^\"']*)[\"']\\s*\\+\\s*[^+]*\\+\\s*[\"']\\.[\"']\\s*\\+\\s*\\{[^}]*[\"']chunk-[a-zA-Z0-9\\-]+[\"']\\s*:\\s*[\"'][a-zA-Z0-9]+[\"']",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );

        Matcher matcher1 = webpackFunctionPattern.matcher(jsContent);
        if (matcher1.find()) {
            String pathPrefix = matcher1.group(1);
            SwingUtilities.invokeLater(() ->
                outputArea.append("  [Webpack] Detected path template: " + pathPrefix + "{chunk}.{hash}.js\n"));
            return pathPrefix + "{chunk}.{hash}.js";
        }

        // Alternative pattern: Look for simpler webpack path constructions
        Pattern simpleWebpackPattern = Pattern.compile(
            "[\"']([^\"']*(?:static|assets)/js/)[\"'].*?\\{[^}]*[\"']chunk-[a-zA-Z0-9\\-]+[\"']\\s*:\\s*[\"'][a-zA-Z0-9]+[\"']",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE | Pattern.DOTALL
        );

        Matcher matcher2 = simpleWebpackPattern.matcher(jsContent);
        if (matcher2.find()) {
            String pathPrefix = matcher2.group(1);
            SwingUtilities.invokeLater(() ->
                outputArea.append("  [Webpack] Detected simple path template: " + pathPrefix + "{chunk}.{hash}.js\n"));
            return pathPrefix + "{chunk}.{hash}.js";
        }

        // Look for any function that mentions both "static/js" and chunk mappings
        Pattern contextualPattern = Pattern.compile(
            "static/js/[\"'][^}]*\\{[^}]*[\"']chunk-[a-zA-Z0-9\\-]+[\"']\\s*:\\s*[\"'][a-zA-Z0-9]+[\"']",
            Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
        );

        if (contextualPattern.matcher(jsContent).find()) {
            SwingUtilities.invokeLater(() ->
                outputArea.append("  [Webpack] Detected static/js pattern, using default template\n"));
            return "static/js/{chunk}.{hash}.js";
        }

        return null; // No webpack path template found
    }

    /**
     * 检查添加新的依赖关系是否会创建循环
     */
    private boolean wouldCreateCycle(String newJsUrl, String currentJsUrl) {
        // 如果新URL就是当前URL，直接返回true（自引用）
        if (newJsUrl.equals(currentJsUrl)) {
            return true;
        }

        // 使用深度优先搜索检测循环
        Set<String> visited = new HashSet<>();
        return hasCycleDFS(newJsUrl, currentJsUrl, visited);
    }

    /**
     * 使用深度优先搜索检测循环依赖
     */
    private boolean hasCycleDFS(String startUrl, String targetUrl, Set<String> visited) {
        // 如果已经访问过这个节点，说明有循环
        if (visited.contains(startUrl)) {
            return true;
        }

        // 如果找到目标节点，说明有路径从target到start，加上start到target的边会形成循环
        if (startUrl.equals(targetUrl)) {
            return true;
        }

        visited.add(startUrl);

        // 检查startUrl的所有依赖
        Set<String> dependencies = jsDependencyGraph.get(startUrl);
        if (dependencies != null) {
            for (String dependency : dependencies) {
                if (hasCycleDFS(dependency, targetUrl, visited)) {
                    return true;
                }
            }
        }

        visited.remove(startUrl); // 回溯
        return false;
    }

    /**
     * 分析JavaScript文件（简化实现）
     */
    private void analyzeJavaScriptFile(String jsUrl) {
        try {
            URL url = new URL(jsUrl);
            IHttpService httpService = helpers.buildHttpService(url.getHost(), getEffectivePort(url), url.getProtocol());

            String path = url.getPath();
            if (path == null || path.isEmpty()) {
                path = "/";
            }

            String requestLine = "GET " + path +
                (url.getQuery() != null ? "?" + url.getQuery() : "") + " HTTP/1.1\r\n";
            String headers = buildHttpHeaders(url.getHost(),
                                            "Mozilla/5.0 (API Security Checker)",
                                            "application/javascript, */*");
            headers += "\r\n";
            byte[] request = (requestLine + headers).getBytes();

            IHttpRequestResponse requestResponse = callbacks.makeHttpRequest(httpService, request);

            if (requestResponse.getResponse() != null) {
                String jsContent = new String(requestResponse.getResponse());

                // 提取响应体（跳过HTTP头部）
                int bodyStart = jsContent.indexOf("\r\n\r\n");
                if (bodyStart != -1) {
                    jsContent = jsContent.substring(bodyStart + 4);
                }

                // 分析JavaScript内容查找API路径
                findApiPaths(jsContent, jsUrl);

                // 如果启用了同步分析，提取敏感信息
                if (syncAnalyzeCheckBox != null && syncAnalyzeCheckBox.isSelected()) {
                    extractSensitiveInfo(jsContent, jsUrl);
                }

                SwingUtilities.invokeLater(() ->
                    outputArea.append("  [JS Analysis] Completed analysis of " + jsUrl + "\n"));
            }

        } catch (Exception e) {
            SwingUtilities.invokeLater(() ->
                outputArea.append("  [JS Analysis] Error analyzing " + jsUrl + ": " + e.getMessage() + "\n"));
        }
    }

    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
    
    /**
     * 发现结果数据类
     */
    private static class DiscoveryResult {
        private String type;
        private String content;
        private String source;
        private String status;
        
        public DiscoveryResult(String type, String content, String source, String status) {
            this.type = type;
            this.content = content;
            this.source = source;
            this.status = status;
        }
        
        // Getters
        public String getType() { return type; }
        public String getContent() { return content; }
        public String getSource() { return source; }
        public String getStatus() { return status; }
    }
}
