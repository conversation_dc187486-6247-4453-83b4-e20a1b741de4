package burp;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableCellRenderer;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 增强版敏感信息提取器 - 参考BurpAPIFinder的界面设计
 * 提供更丰富的功能和更好的用户体验
 */
public class EnhancedSensitiveInfoExtractor {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private ApiScanMainTab apiScanMainTab; // 新增：API扫描主界面引用
    
    // 主界面组件
    private JPanel mainPanel;
    private JSplitPane mainSplitPane;
    
    // 配置面板组件 - 参考BurpAPIFinder的ConfigPanel
    private JPanel configPanel;
    private JTextField urlField;
    private JButton analyzeButton;
    private JToggleButton toggleButton;  // 开启/关闭按钮
    private JToggleButton flashButton;   // 刷新控制按钮
    private JCheckBox autoAnalyzeJSCheckBox;
    private JCheckBox realTimeAnalysisCheckBox;
    private JComboBox<String> filterComboBox;  // 过滤选择框
    private JComboBox<String> hostFilterComboBox;  // 新增：host过滤选择框
    private JTextField searchField;  // 搜索框
    private JButton searchButton;
    
    // 统计信息标签
    private JLabel totalCountLabel;
    private JLabel foundCountLabel;
    private JLabel analyzedCountLabel;
    private JLabel jsCountLabel;
    private JLabel statusLabel;
    private JProgressBar progressBar;
    
    // 数据展示组件
    private DefaultTableModel tableModel;
    private JTable dataTable;
    private JScrollPane tableScrollPane;
    
    // 详情展示组件
    private JTextArea detailArea;
    private JScrollPane detailScrollPane;
    
    // 数据存储
    private Map<String, List<SensitiveDataItem>> sensitiveData = new HashMap<>();
    private Set<String> processedUrls = new HashSet<>();
    private Set<String> discoveredHosts = new HashSet<>(); // 新增：存储发现的host
    private int totalRequests = 0;
    private int foundItems = 0;
    private int analyzedUrls = 0;
    private int jsFiles = 0;
    
    // 敏感信息数据项
    public static class SensitiveDataItem {
        public String type;
        public String value;
        public String source;
        public String url;
        public String description;
        public LocalDateTime foundTime;
        public boolean isImportant;
        
        public SensitiveDataItem(String type, String value, String source, String url, String description) {
            this.type = type;
            this.value = value;
            this.source = source;
            this.url = url;
            this.description = description;
            this.foundTime = LocalDateTime.now();
            this.isImportant = isImportantType(type);
        }
        
        private boolean isImportantType(String type) {
            return type.contains("密钥") || type.contains("Token") || type.contains("密码") || 
                   type.contains("私钥") || type.contains("API") || type.contains("身份证");
        }
    }
    
    // 敏感信息模式类
    private static class SensitivePattern {
        final String name;
        final Pattern pattern;
        final String description;
        
        SensitivePattern(String name, String regex, String description) {
            this.name = name;
            this.pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
            this.description = description;
        }
    }
    
    // 敏感信息检测规则 - 集成Chrome插件background.js规则
    private static final SensitivePattern[] SENSITIVE_PATTERNS = {
        // === Chrome插件核心规则集成 ===
        // 对应 background.js 中的 ["ip","ip_port","domain","path","incomplete_path","url","sfz","mobile","mail","jwt","algorithm","secret"]

        // IP相关 (对应 "ip") - 保持分组功能
        new SensitivePattern("IP地址",
            "\\b((?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\\b",
            "IPv4地址 - Chrome插件规则"),

        // IP+端口 (对应 "ip_port") - 保持分组功能
        new SensitivePattern("IP+端口",
            "\\b((?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?):[0-9]{1,5})\\b",
            "IP地址和端口组合 - Chrome插件规则"),

        // 域名 (对应 "domain") - 保持分组功能
        new SensitivePattern("域名",
            "\\b([a-zA-Z0-9](?:[a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|cn|net|org|gov|edu|mil|int|co|io|me|tv|cc|tk|ml|ga|cf|biz|info|name|mobi|asia|tel|travel|museum|aero|jobs|cat|pro|xxx|post|geo|local)\\b",
            "域名地址 - Chrome插件规则"),

        // 路径 (对应 "path") - 保持分组功能
        new SensitivePattern("API路径",
            "(?:[\"']|\\b)((?:/[a-zA-Z0-9._\\-~!*'();:@&=+$,/?#\\[\\]%]+){2,})(?:[\"']|\\b)",
            "API路径或文件路径 - Chrome插件规则"),

        // 不完整路径 (对应 "incomplete_path") - 保持分组功能
        new SensitivePattern("相对路径",
            "(?:[\"']|\\b)((?:\\./|\\.\\./|/)[a-zA-Z0-9._\\-~!*'();:@&=+$,/?#\\[\\]%]*)(?:[\"']|\\b)",
            "相对路径或不完整路径 - Chrome插件规则"),

        // URL (对应 "url") - 保持分组功能
        new SensitivePattern("完整URL",
            "\\b((?:https?|ftp|file)://[a-zA-Z0-9\\-._~:/?#\\[\\]@!$&'()*+,;=%]+)",
            "完整URL地址 - Chrome插件规则"),

        // 身份证 (对应 "sfz")
        new SensitivePattern("身份证号",
            "\\b([1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])\\b",
            "中国大陆身份证号码 - Chrome插件规则"),

        // 手机号 (对应 "mobile")
        new SensitivePattern("手机号码",
            "\\b(1[3-9]\\d{9})\\b",
            "中国大陆手机号码 - Chrome插件规则"),

        // 邮箱 (对应 "mail")
        new SensitivePattern("邮箱地址",
            "\\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})\\b",
            "电子邮件地址 - Chrome插件规则"),

        // JWT (对应 "jwt") - 保持分组功能
        new SensitivePattern("JWT Token",
            "(eyJ[A-Za-z0-9_/+-]{10,}={0,2}\\.[A-Za-z0-9_/+-]{15,}={0,2}\\.[A-Za-z0-9_/+-]{10,}={0,2})",
            "JSON Web Token - Chrome插件规则"),

        // 算法相关 (对应 "algorithm") - 保持分组功能
        new SensitivePattern("加密算法",
            "\\b(AES|DES|3DES|RSA|MD5|SHA1|SHA256|SHA512|HMAC|Base64|bcrypt|scrypt|PBKDF2|blowfish|twofish)\\b",
            "加密算法标识 - Chrome插件规则"),

        // 密钥相关 (对应 "secret") - 保持分组功能
        new SensitivePattern("密钥配置",
            "(?i)(?:secret|key|password|pwd|pass|token|auth|api_key|apikey|access_key|private_key|public_key)\\s*[:=]\\s*[\"']?([a-zA-Z0-9\\-_+/=]{8,})[\"']?",
            "密钥和配置信息 - Chrome插件规则"),

        // === 扩展的敏感信息检测规则 ===
        
        new SensitivePattern("Bearer Token", 
            "\\b[Bb]earer\\s+[a-zA-Z0-9\\-=._+/\\\\]{20,500}\\b", 
            "Bearer认证令牌"),
        
        new SensitivePattern("Basic Auth", 
            "\\b[Bb]asic\\s+[A-Za-z0-9+/]{18,}={0,2}\\b", 
            "Basic认证"),
        
        new SensitivePattern("Authorization Token", 
            "[\"'\\[]*[Aa]uthorization[\"'\\]]*\\s*[:=]\\s*['\"]?\\b(?:[Tt]oken\\s+)?[a-zA-Z0-9\\-_+/]{20,500}['\"]?", 
            "认证令牌"),
        
        // API密钥
        new SensitivePattern("AWS Access Key", 
            "[\"'](?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16}[\"']", 
            "AWS访问密钥"),
        
        new SensitivePattern("阿里云AccessKey", 
            "\\bLTAI[A-Za-z\\d]{12,30}\\b", 
            "阿里云访问密钥"),
        
        new SensitivePattern("腾讯云AccessKey", 
            "\\bAKID[A-Za-z\\d]{13,40}\\b", 
            "腾讯云访问密钥"),
        
        new SensitivePattern("Google API Key", 
            "\\bAIza[0-9A-Za-z_\\-]{35}\\b", 
            "Google API密钥"),
        
        new SensitivePattern("GitHub Token", 
            "\\b((?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255})\\b", 
            "GitHub访问令牌"),
        
        // 私钥和证书
        new SensitivePattern("私钥", 
            "-----\\s*?BEGIN[ A-Z0-9_-]*?PRIVATE KEY\\s*?-----[a-zA-Z0-9\\/\\n\\r=+]*-----\\s*?END[ A-Z0-9_-]*? PRIVATE KEY\\s*?-----", 
            "SSH/TLS私钥"),
        
        // 密码配置
        new SensitivePattern("密码配置", 
            "(?i)(?:admin_?pass|password|[a-z]{3,15}_?password|user_?pass|user_?pwd|admin_?pwd)\\\\?['\"]\\s*[:=]\\s*\\\\?['\"][a-z0-9!@#$%&*]{5,20}\\\\?['\"]", 
            "配置文件中的密码"),
        
        // Webhook URLs
        new SensitivePattern("企业微信Webhook", 
            "\\bhttps://qyapi.weixin.qq.com/cgi-bin/webhook/send\\?key=[a-zA-Z0-9\\-]{25,50}\\b", 
            "企业微信Webhook地址"),
        
        new SensitivePattern("钉钉Webhook", 
            "\\bhttps://oapi.dingtalk.com/robot/send\\?access_token=[a-z0-9]{50,80}\\b", 
            "钉钉Webhook地址"),
        
        new SensitivePattern("飞书Webhook", 
            "\\bhttps://open.feishu.cn/open-apis/bot/v2/hook/[a-z0-9\\-]{25,50}\\b", 
            "飞书Webhook地址"),
        
        new SensitivePattern("Slack Webhook", 
            "\\bhttps://hooks.slack.com/services/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{15,24}\\b", 
            "Slack Webhook地址"),
        
        // 微信相关
        new SensitivePattern("微信AppID", 
            "[\"'](wx[a-z0-9]{15,18})[\"']", 
            "微信公众号/小程序AppID"),
        
        new SensitivePattern("企业微信CorpID", 
            "[\"'](ww[a-z0-9]{15,18})[\"']", 
            "企业微信CorpID"),
        
        // 应用配置密钥
        new SensitivePattern("应用配置密钥", 
            "\\b(?:VUE|APP|REACT)_[A-Z_0-9]{1,15}_(?:KEY|PASS|PASSWORD|TOKEN|APIKEY)['\"][:=]\"(?:[A-Za-z0-9_\\-]{15,50}|[a-z0-9/+]{50,100}==?)\"", 
            "前端应用配置密钥")
    };
    
    public EnhancedSensitiveInfoExtractor(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
    }

    /**
     * 设置API扫描主界面引用
     */
    public void setApiScanMainTab(ApiScanMainTab apiScanMainTab) {
        this.apiScanMainTab = apiScanMainTab;
        initializeUI();
        initializeData();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建配置面板
        configPanel = createConfigPanel();
        mainPanel.add(configPanel, BorderLayout.NORTH);
        
        // 创建主分割面板
        mainSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        mainSplitPane.setResizeWeight(0.7); // 上部分占70%
        
        // 创建数据表格面板
        JPanel tablePanel = createTablePanel();
        mainSplitPane.setTopComponent(tablePanel);
        
        // 创建详情面板
        JPanel detailPanel = createDetailPanel();
        mainSplitPane.setBottomComponent(detailPanel);
        
        mainPanel.add(mainSplitPane, BorderLayout.CENTER);
        
        // 创建状态面板
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        // 显示初始说明
        showWelcomeMessage();
    }
    
    /**
     * 创建配置面板 - 参考BurpAPIFinder的ConfigPanel设计
     */
    private JPanel createConfigPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("敏感信息提取配置"));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 第一行：统计信息
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("请求总数:"), gbc);

        gbc.gridx = 1;
        totalCountLabel = new JLabel("0");
        totalCountLabel.setForeground(new Color(0, 0, 255));
        panel.add(totalCountLabel, gbc);

        gbc.gridx = 2;
        panel.add(new JLabel("发现敏感信息:"), gbc);

        gbc.gridx = 3;
        foundCountLabel = new JLabel("0");
        foundCountLabel.setForeground(new Color(0, 255, 0));
        panel.add(foundCountLabel, gbc);

        gbc.gridx = 4;
        panel.add(new JLabel("已分析URL:"), gbc);

        gbc.gridx = 5;
        analyzedCountLabel = new JLabel("0/0");
        analyzedCountLabel.setForeground(new Color(0, 0, 255));
        panel.add(analyzedCountLabel, gbc);

        gbc.gridx = 6;
        panel.add(new JLabel("JS文件:"), gbc);

        gbc.gridx = 7;
        jsCountLabel = new JLabel("0/0");
        jsCountLabel.setForeground(new Color(0, 0, 255));
        panel.add(jsCountLabel, gbc);

        // 添加弹性空间
        gbc.gridx = 8; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(Box.createHorizontalGlue(), gbc);

        // 控制按钮
        gbc.gridx = 9; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        toggleButton = new JToggleButton("启动");
        toggleButton.setPreferredSize(new Dimension(60, 24));
        toggleButton.setToolTipText("启动/停止敏感信息提取");
        toggleButton.setBackground(new Color(240, 255, 240));
        toggleButton.setBorder(BorderFactory.createRaisedBevelBorder());
        toggleButton.addActionListener(e -> toggleAnalysis());
        panel.add(toggleButton, gbc);

        gbc.gridx = 10;
        flashButton = new JToggleButton("自动");
        flashButton.setPreferredSize(new Dimension(50, 24));
        flashButton.setToolTipText("控制表格是否自动刷新");
        flashButton.setBackground(new Color(255, 248, 220));
        flashButton.setBorder(BorderFactory.createRaisedBevelBorder());
        flashButton.setSelected(true);
        panel.add(flashButton, gbc);

        // 第二行：URL输入和选项
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("目标URL:"), gbc);

        gbc.gridx = 1; gbc.gridwidth = 4; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        urlField = new JTextField();
        urlField.setToolTipText("输入要分析的网站URL，支持HTTP/HTTPS");
        panel.add(urlField, gbc);

        gbc.gridx = 5; gbc.gridwidth = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        analyzeButton = new JButton("🚀 开始分析");
        analyzeButton.setBackground(new Color(70, 130, 180));
        analyzeButton.setForeground(Color.WHITE);
        analyzeButton.setBorder(BorderFactory.createRaisedBevelBorder());
        analyzeButton.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
        analyzeButton.addActionListener(e -> startAnalysis());
        panel.add(analyzeButton, gbc);

        // 选项复选框
        gbc.gridx = 6;
        autoAnalyzeJSCheckBox = new JCheckBox("自动分析JS", true);
        autoAnalyzeJSCheckBox.setToolTipText("自动发现并分析页面中引用的JavaScript文件");
        panel.add(autoAnalyzeJSCheckBox, gbc);

        gbc.gridx = 7;
        realTimeAnalysisCheckBox = new JCheckBox("实时监控", false);
        realTimeAnalysisCheckBox.setToolTipText("实时监控Burp代理流量中的敏感信息");
        realTimeAnalysisCheckBox.addActionListener(e -> toggleRealTimeAnalysis());
        panel.add(realTimeAnalysisCheckBox, gbc);

        // 第四行：高级选项
        gbc.gridx = 0; gbc.gridy = 3; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("高级选项:"), gbc);

        gbc.gridx = 1;
        JCheckBox fastModeCheckBox = new JCheckBox("快速模式", false);
        fastModeCheckBox.setToolTipText("启用快速模式：大文件截取分析、并发处理、超时控制");
        panel.add(fastModeCheckBox, gbc);

        gbc.gridx = 2;
        JCheckBox skipLargeFilesCheckBox = new JCheckBox("跳过大文件", false);
        skipLargeFilesCheckBox.setToolTipText("跳过超过1MB的JS文件以提高速度");
        panel.add(skipLargeFilesCheckBox, gbc);

        gbc.gridx = 3;
        JLabel timeoutLabel = new JLabel("超时(秒):");
        panel.add(timeoutLabel, gbc);

        gbc.gridx = 4;
        JSpinner timeoutSpinner = new JSpinner(new SpinnerNumberModel(30, 5, 120, 5));
        timeoutSpinner.setPreferredSize(new Dimension(60, 24));
        timeoutSpinner.setToolTipText("HTTP请求超时时间（秒）");
        panel.add(timeoutSpinner, gbc);

        // 第三行：过滤和搜索
        gbc.gridx = 0; gbc.gridy = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("显示:"), gbc);

        gbc.gridx = 1;
        filterComboBox = new JComboBox<>(new String[]{"全部", "仅重要", "仅Token", "仅密钥", "仅身份信息", "仅网络信息", "仅Chrome规则", "仅算法相关", "仅路径信息"});
        filterComboBox.addActionListener(e -> applyFilter());
        panel.add(filterComboBox, gbc);

        // 新增：Host过滤器
        gbc.gridx = 2; gbc.gridwidth = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("Host:"), gbc);

        gbc.gridx = 3;
        hostFilterComboBox = new JComboBox<>(new String[]{"全部Host"});
        hostFilterComboBox.setToolTipText("按Host过滤敏感信息");
        hostFilterComboBox.addActionListener(e -> applyFilter());
        panel.add(hostFilterComboBox, gbc);

        // 添加弹性空间
        gbc.gridx = 4; gbc.gridwidth = 2; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(Box.createHorizontalGlue(), gbc);

        gbc.gridx = 6; gbc.gridwidth = 1; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("搜索:"), gbc);

        gbc.gridx = 7;
        searchField = new JTextField(15);
        searchField.setToolTipText("搜索敏感信息内容");
        panel.add(searchField, gbc);

        gbc.gridx = 8;
        searchButton = new JButton("🔍");
        searchButton.setPreferredSize(new Dimension(30, 24));
        searchButton.setToolTipText("搜索敏感信息");
        searchButton.setBackground(new Color(240, 248, 255));
        searchButton.setBorder(BorderFactory.createRaisedBevelBorder());
        searchButton.addActionListener(e -> performSearch());
        panel.add(searchButton, gbc);

        return panel;
    }

    /**
     * 创建数据表格面板
     */
    private JPanel createTablePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("敏感信息列表"));

        // 创建表格模型
        String[] columnNames = {"#", "类型", "敏感信息", "来源", "URL", "重要性", "发现时间", "描述"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };

        // 创建表格
        dataTable = new JTable(tableModel) {
            @Override
            public String getToolTipText(MouseEvent e) {
                int row = rowAtPoint(e.getPoint());
                int col = columnAtPoint(e.getPoint());
                if (row > -1 && col > -1) {
                    Object value = getValueAt(row, col);
                    return value == null ? null : value.toString();
                }
                return super.getToolTipText(e);
            }
        };

        dataTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        dataTable.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);

        // 美化表格样式
        dataTable.setRowHeight(25);
        dataTable.setShowGrid(true);
        dataTable.setGridColor(new Color(230, 230, 230));
        dataTable.setSelectionBackground(new Color(184, 207, 229));
        dataTable.setSelectionForeground(Color.BLACK);
        dataTable.getTableHeader().setBackground(new Color(240, 248, 255));
        dataTable.getTableHeader().setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));

        // 设置列宽
        dataTable.getColumnModel().getColumn(0).setPreferredWidth(50);   // #
        dataTable.getColumnModel().getColumn(1).setPreferredWidth(100);  // 类型
        dataTable.getColumnModel().getColumn(2).setPreferredWidth(200);  // 敏感信息
        dataTable.getColumnModel().getColumn(3).setPreferredWidth(80);   // 来源
        dataTable.getColumnModel().getColumn(4).setPreferredWidth(150);  // URL
        dataTable.getColumnModel().getColumn(5).setPreferredWidth(60);   // 重要性
        dataTable.getColumnModel().getColumn(6).setPreferredWidth(120);  // 发现时间
        dataTable.getColumnModel().getColumn(7).setPreferredWidth(150);  // 描述

        // 设置单元格渲染器
        dataTable.setDefaultRenderer(Object.class, new SensitiveInfoCellRenderer());

        // 添加行选择监听器
        dataTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showSelectedItemDetail();
            }
        });

        // 创建右键菜单
        createTablePopupMenu();

        tableScrollPane = new JScrollPane(dataTable);
        tableScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        panel.add(tableScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建详情面板
     */
    private JPanel createDetailPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("详细信息"));

        detailArea = new JTextArea();
        detailArea.setEditable(false);
        detailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailArea.setBackground(new Color(248, 248, 248));
        detailArea.setText("选择上方表格中的项目查看详细信息...");

        detailScrollPane = new JScrollPane(detailArea);
        detailScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        detailScrollPane.setPreferredSize(new Dimension(800, 150));

        panel.add(detailScrollPane, BorderLayout.CENTER);

        // 添加操作按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton copyButton = new JButton("📋 复制详情");
        copyButton.setBackground(new Color(240, 248, 255));
        copyButton.setBorder(BorderFactory.createRaisedBevelBorder());
        copyButton.addActionListener(e -> copyDetailToClipboard());
        buttonPanel.add(copyButton);

        JButton exportButton = new JButton("📤 导出选中");
        exportButton.setBackground(new Color(240, 255, 240));
        exportButton.setBorder(BorderFactory.createRaisedBevelBorder());
        exportButton.addActionListener(e -> exportSelectedItem());
        buttonPanel.add(exportButton);

        JButton clearButton = new JButton("🗑️ 清除结果");
        clearButton.setBackground(new Color(255, 240, 240));
        clearButton.setBorder(BorderFactory.createRaisedBevelBorder());
        clearButton.addActionListener(e -> clearResults());
        buttonPanel.add(clearButton);

        JButton exportAllButton = new JButton("📊 导出全部");
        exportAllButton.setBackground(new Color(255, 248, 220));
        exportAllButton.setBorder(BorderFactory.createRaisedBevelBorder());
        exportAllButton.addActionListener(e -> exportAllResults());
        buttonPanel.add(exportAllButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建状态面板
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);

        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("等待开始...");
        panel.add(progressBar, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建表格右键菜单
     */
    private void createTablePopupMenu() {
        JPopupMenu popupMenu = new JPopupMenu();

        JMenuItem copyItem = new JMenuItem("复制敏感信息");
        copyItem.addActionListener(e -> copySelectedSensitiveInfo());
        popupMenu.add(copyItem);

        JMenuItem copyUrlItem = new JMenuItem("复制URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        popupMenu.add(copyUrlItem);

        popupMenu.addSeparator();

        JMenuItem markImportantItem = new JMenuItem("标记为重要");
        markImportantItem.addActionListener(e -> markSelectedAsImportant());
        popupMenu.add(markImportantItem);

        JMenuItem deleteItem = new JMenuItem("删除");
        deleteItem.addActionListener(e -> deleteSelectedItem());
        popupMenu.add(deleteItem);

        popupMenu.addSeparator();

        JMenuItem exportItem = new JMenuItem("导出选中项");
        exportItem.addActionListener(e -> exportSelectedItem());
        popupMenu.add(exportItem);

        dataTable.setComponentPopupMenu(popupMenu);
    }

    /**
     * 敏感信息表格单元格渲染器
     */
    private class SensitiveInfoCellRenderer extends DefaultTableCellRenderer {
        @Override
        public Component getTableCellRendererComponent(JTable table, Object value,
                boolean isSelected, boolean hasFocus, int row, int column) {

            Component c = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (!isSelected) {
                // 根据重要性设置背景色
                String importance = (String) table.getValueAt(row, 5);
                if ("高".equals(importance)) {
                    c.setBackground(new Color(255, 240, 240)); // 浅红色
                } else if ("中".equals(importance)) {
                    c.setBackground(new Color(255, 255, 240)); // 浅黄色
                } else {
                    c.setBackground(Color.WHITE);
                }

                // 根据类型设置前景色
                String type = (String) table.getValueAt(row, 1);
                if (type.contains("Token") || type.contains("密钥") || type.contains("私钥")) {
                    c.setForeground(new Color(255, 0, 0)); // 红色
                } else if (type.contains("身份证") || type.contains("手机")) {
                    c.setForeground(new Color(255, 165, 0)); // 橙色
                } else {
                    c.setForeground(Color.BLACK);
                }
            }

            return c;
        }
    }

    /**
     * 初始化数据结构
     */
    private void initializeData() {
        // 初始化敏感数据存储
        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            sensitiveData.put(pattern.name, new ArrayList<>());
        }
    }

    /**
     * 显示欢迎信息
     */
    private void showWelcomeMessage() {
        detailArea.append("🔍 增强版敏感信息提取工具\n");
        detailArea.append(repeat("=", 50) + "\n");
        detailArea.append("支持检测的敏感信息类型:\n\n");

        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            detailArea.append("• " + pattern.name + " - " + pattern.description + "\n");
        }

        detailArea.append("\n使用说明:\n");
        detailArea.append("1. 在目标URL输入框中输入要分析的网站地址\n");
        detailArea.append("2. 选择是否自动分析JS文件\n");
        detailArea.append("3. 点击'开始分析'按钮启动扫描\n");
        detailArea.append("4. 使用过滤和搜索功能快速定位感兴趣的信息\n");
        detailArea.append("5. 双击表格行查看详细信息\n");
        detailArea.append("6. 右键点击表格行进行更多操作\n\n");
        detailArea.append("准备就绪，等待开始分析...\n");
        detailArea.setCaretPosition(0);
    }

    // ==================== 事件处理方法 ====================

    /**
     * 切换分析状态
     */
    private void toggleAnalysis() {
        if (toggleButton.isSelected()) {
            toggleButton.setText("停止");
            statusLabel.setText("分析已启动");
            // 这里可以添加启动实时监控的逻辑
        } else {
            toggleButton.setText("启动");
            statusLabel.setText("分析已停止");
            // 这里可以添加停止实时监控的逻辑
        }
    }

    /**
     * 开始分析
     */
    private void startAnalysis() {
        String url = urlField.getText().trim();
        if (url.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入目标URL", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // 自动补全协议
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
            urlField.setText(url);
        }

        final String targetUrl = url;

        // 禁用按钮，开始分析
        analyzeButton.setEnabled(false);
        updateStatus("正在分析: " + targetUrl);

        // 后台线程执行分析
        new Thread(() -> {
            try {
                performAnalysis(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    updateStatus("分析失败: " + e.getMessage());
                    callbacks.printError("Analysis failed: " + e.getMessage());
                });
            } finally {
                SwingUtilities.invokeLater(() -> {
                    analyzeButton.setEnabled(true);
                    progressBar.setValue(0);
                    progressBar.setString("分析完成");
                });
            }
        }).start();
    }

    /**
     * 切换实时分析
     */
    private void toggleRealTimeAnalysis() {
        if (realTimeAnalysisCheckBox.isSelected()) {
            statusLabel.setText("实时监控已启用");
            // 这里可以添加实时监控逻辑
        } else {
            statusLabel.setText("实时监控已禁用");
        }
    }

    /**
     * 应用过滤器 - 支持类型过滤和Host过滤
     */
    private void applyFilter() {
        updateTableDisplay();
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        String searchText = searchField.getText().trim();
        if (searchText.isEmpty()) {
            applyFilter(); // 如果搜索为空，则显示所有符合过滤条件的项
            return;
        }

        // 这里实现搜索逻辑
        updateTableDisplay();
    }

    /**
     * 显示选中项详情
     */
    private void showSelectedItemDetail() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow == -1) {
            return;
        }

        String type = (String) dataTable.getValueAt(selectedRow, 1);
        String value = (String) dataTable.getValueAt(selectedRow, 2);
        String source = (String) dataTable.getValueAt(selectedRow, 3);
        String url = (String) dataTable.getValueAt(selectedRow, 4);
        String importance = (String) dataTable.getValueAt(selectedRow, 5);
        String time = (String) dataTable.getValueAt(selectedRow, 6);
        String description = (String) dataTable.getValueAt(selectedRow, 7);

        StringBuilder detail = new StringBuilder();
        detail.append("敏感信息详情\n");
        detail.append(repeat("=", 50) + "\n");
        detail.append("类型: ").append(type).append("\n");
        detail.append("内容: ").append(value).append("\n");
        detail.append("来源: ").append(source).append("\n");
        detail.append("URL: ").append(url).append("\n");
        detail.append("重要性: ").append(importance).append("\n");
        detail.append("发现时间: ").append(time).append("\n");
        detail.append("描述: ").append(description).append("\n\n");

        // 添加安全建议
        detail.append("安全建议:\n");
        detail.append(repeat("-", 20) + "\n");
        if (type.contains("Token") || type.contains("密钥")) {
            detail.append("• 立即更换此Token/密钥\n");
            detail.append("• 检查是否有未授权访问\n");
            detail.append("• 加强访问控制和权限管理\n");
        } else if (type.contains("身份证") || type.contains("手机")) {
            detail.append("• 检查个人信息保护措施\n");
            detail.append("• 确保符合数据保护法规\n");
            detail.append("• 考虑数据脱敏处理\n");
        } else if (type.contains("密码")) {
            detail.append("• 立即更改相关密码\n");
            detail.append("• 检查密码存储方式\n");
            detail.append("• 实施密码加密存储\n");
        }

        detailArea.setText(detail.toString());
        detailArea.setCaretPosition(0);
    }

    /**
     * 更新状态
     */
    private void updateStatus(String status) {
        statusLabel.setText(status);
    }

    /**
     * 更新表格显示
     */
    private void updateTableDisplay() {
        // 清空现有数据
        tableModel.setRowCount(0);

        String selectedFilter = (String) filterComboBox.getSelectedItem();
        String selectedHost = (String) hostFilterComboBox.getSelectedItem();
        String searchText = searchField.getText().trim().toLowerCase();

        int rowIndex = 1;
        for (Map.Entry<String, List<SensitiveDataItem>> entry : sensitiveData.entrySet()) {
            for (SensitiveDataItem item : entry.getValue()) {
                // 应用类型过滤器
                if (!matchesFilter(item, selectedFilter)) {
                    continue;
                }

                // 应用Host过滤器
                if (!matchesHostFilter(item, selectedHost)) {
                    continue;
                }

                // 应用搜索
                if (!searchText.isEmpty() && !matchesSearch(item, searchText)) {
                    continue;
                }

                String importance = item.isImportant ? "高" : "低";
                String timeStr = item.foundTime.format(DateTimeFormatter.ofPattern("MM-dd HH:mm:ss"));

                Object[] rowData = {
                    rowIndex++,
                    item.type,
                    item.value,
                    item.source,
                    item.url,
                    importance,
                    timeStr,
                    item.description
                };

                tableModel.addRow(rowData);
            }
        }

        // 更新统计信息
        updateStatistics();
    }

    /**
     * 检查项目是否匹配过滤条件 - 支持Chrome插件规则
     */
    private boolean matchesFilter(SensitiveDataItem item, String filter) {
        switch (filter) {
            case "全部":
                return true;
            case "仅重要":
                return item.isImportant;
            case "仅Token":
                return item.type.contains("Token") || item.type.contains("JWT") || item.type.contains("Basic Auth");
            case "仅密钥":
                return item.type.contains("密钥") || item.type.contains("Key") || item.type.contains("密码配置") || item.type.contains("私钥");
            case "仅身份信息":
                return item.type.contains("身份证") || item.type.contains("手机") || item.type.contains("邮箱");
            case "仅网络信息":
                return item.type.contains("IP") || item.type.contains("域名") || item.type.contains("URL") || item.type.contains("路径");
            case "仅Chrome规则":
                return item.description.contains("Chrome插件规则");
            case "仅算法相关":
                return item.type.contains("算法") || item.type.contains("加密");
            case "仅路径信息":
                return item.type.contains("路径") || item.type.contains("URL") || item.type.contains("域名");
            default:
                return true;
        }
    }

    /**
     * 检查项目是否匹配Host过滤条件
     */
    private boolean matchesHostFilter(SensitiveDataItem item, String hostFilter) {
        if (hostFilter == null || hostFilter.equals("全部Host")) {
            return true;
        }

        String itemHost = extractHost(item.url);
        return itemHost.equals(hostFilter);
    }

    /**
     * 检查项目是否匹配搜索条件
     */
    private boolean matchesSearch(SensitiveDataItem item, String searchText) {
        return item.type.toLowerCase().contains(searchText) ||
               item.value.toLowerCase().contains(searchText) ||
               item.source.toLowerCase().contains(searchText) ||
               item.url.toLowerCase().contains(searchText) ||
               item.description.toLowerCase().contains(searchText);
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics() {
        totalCountLabel.setText(String.valueOf(totalRequests));
        foundCountLabel.setText(String.valueOf(foundItems));
        analyzedCountLabel.setText(analyzedUrls + "/" + processedUrls.size());
        jsCountLabel.setText(jsFiles + "/" + jsFiles);
    }

    /**
     * 字符串重复方法（Java 8兼容）
     */
    private String repeat(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    // ==================== 右键菜单方法 ====================

    /**
     * 复制选中的敏感信息
     */
    private void copySelectedSensitiveInfo() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow != -1) {
            String value = (String) dataTable.getValueAt(selectedRow, 2);
            copyToClipboard(value);
            statusLabel.setText("已复制敏感信息到剪贴板");
        }
    }

    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow != -1) {
            String url = (String) dataTable.getValueAt(selectedRow, 4);
            copyToClipboard(url);
            statusLabel.setText("已复制URL到剪贴板");
        }
    }

    /**
     * 标记选中项为重要
     */
    private void markSelectedAsImportant() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow != -1) {
            dataTable.setValueAt("高", selectedRow, 5);
            statusLabel.setText("已标记为重要");
            // 这里可以更新底层数据结构
        }
    }

    /**
     * 删除选中项
     */
    private void deleteSelectedItem() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow != -1) {
            int result = JOptionPane.showConfirmDialog(mainPanel,
                "确定要删除这个敏感信息项吗？", "确认删除",
                JOptionPane.YES_NO_OPTION);
            if (result == JOptionPane.YES_OPTION) {
                tableModel.removeRow(selectedRow);
                foundItems--;
                updateStatistics();
                statusLabel.setText("已删除选中项");
            }
        }
    }

    // ==================== 按钮操作方法 ====================

    /**
     * 复制详情到剪贴板
     */
    private void copyDetailToClipboard() {
        String detail = detailArea.getText();
        if (!detail.isEmpty()) {
            copyToClipboard(detail);
            statusLabel.setText("已复制详情到剪贴板");
        }
    }

    /**
     * 导出选中项
     */
    private void exportSelectedItem() {
        int selectedRow = dataTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要导出的项目", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setSelectedFile(new File("sensitive_info_item.txt"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                File file = fileChooser.getSelectedFile();
                FileWriter writer = new FileWriter(file);

                // 写入选中项的详细信息
                String detail = detailArea.getText();
                writer.write(detail);

                writer.close();
                statusLabel.setText("已导出到: " + file.getAbsolutePath());
            } catch (IOException e) {
                JOptionPane.showMessageDialog(mainPanel, "导出失败: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 清除所有结果
     */
    private void clearResults() {
        int result = JOptionPane.showConfirmDialog(mainPanel,
            "确定要清除所有结果吗？", "确认清除",
            JOptionPane.YES_NO_OPTION);
        if (result == JOptionPane.YES_OPTION) {
            tableModel.setRowCount(0);
            sensitiveData.clear();
            processedUrls.clear();
            discoveredHosts.clear(); // 清除host信息
            totalRequests = 0;
            foundItems = 0;
            analyzedUrls = 0;
            jsFiles = 0;

            // 重置host过滤器
            SwingUtilities.invokeLater(() -> {
                hostFilterComboBox.removeAllItems();
                hostFilterComboBox.addItem("全部Host");
            });

            updateStatistics();
            detailArea.setText("结果已清除，等待新的分析...");
            statusLabel.setText("已清除所有结果");
        }
    }

    /**
     * 导出所有结果
     */
    private void exportAllResults() {
        if (tableModel.getRowCount() == 0) {
            JOptionPane.showMessageDialog(mainPanel, "没有可导出的数据", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setSelectedFile(new File("sensitive_info_all.txt"));

        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                File file = fileChooser.getSelectedFile();
                FileWriter writer = new FileWriter(file);

                writer.write("敏感信息提取报告\n");
                writer.write(repeat("=", 50) + "\n");
                writer.write("生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
                writer.write("总计发现: " + foundItems + " 项敏感信息\n\n");

                // 导出表格数据
                for (int i = 0; i < tableModel.getRowCount(); i++) {
                    writer.write("项目 " + (i + 1) + ":\n");
                    writer.write("类型: " + tableModel.getValueAt(i, 1) + "\n");
                    writer.write("内容: " + tableModel.getValueAt(i, 2) + "\n");
                    writer.write("来源: " + tableModel.getValueAt(i, 3) + "\n");
                    writer.write("URL: " + tableModel.getValueAt(i, 4) + "\n");
                    writer.write("重要性: " + tableModel.getValueAt(i, 5) + "\n");
                    writer.write("发现时间: " + tableModel.getValueAt(i, 6) + "\n");
                    writer.write("描述: " + tableModel.getValueAt(i, 7) + "\n");
                    writer.write(repeat("-", 30) + "\n");
                }

                writer.close();
                statusLabel.setText("已导出到: " + file.getAbsolutePath());
            } catch (IOException e) {
                JOptionPane.showMessageDialog(mainPanel, "导出失败: " + e.getMessage(),
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 复制文本到剪贴板
     */
    private void copyToClipboard(String text) {
        try {
            Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(new java.awt.datatransfer.StringSelection(text), null);
        } catch (Exception e) {
            callbacks.printError("Failed to copy to clipboard: " + e.getMessage());
        }
    }

    // ==================== 核心分析方法 ====================

    /**
     * 执行分析 - 修复版，正确处理状态同步
     */
    private void performAnalysis(String targetUrl) {
        try {
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(0);
                progressBar.setString("开始分析...");
                updateStatus("开始分析: " + targetUrl);
            });

            // 1. 获取主页面内容
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(10);
                progressBar.setString("正在获取主页面内容...");
            });

            String mainPageContent = getPageContent(targetUrl);
            if (mainPageContent != null) {
                synchronized (this) {
                    totalRequests++;
                    processedUrls.add(targetUrl);
                }

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(30);
                    progressBar.setString("正在分析主页面...");
                    updateStatus("主页面加载完成 (" + (mainPageContent.length() / 1024) + "KB)，开始分析...");
                });

                // 分析主页面
                analyzeContent(mainPageContent, "[主页面]", targetUrl);

                SwingUtilities.invokeLater(() -> {
                    updateStatistics();
                    updateStatus("主页面分析完成");
                });

                // 2. 如果启用了JS分析，查找并分析JS文件
                if (autoAnalyzeJSCheckBox.isSelected()) {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(40);
                        progressBar.setString("正在查找JS文件...");
                    });

                    Set<String> jsUrls = extractJavaScriptUrls(mainPageContent, targetUrl);
                    jsFiles = jsUrls.size();

                    SwingUtilities.invokeLater(() -> {
                        updateStatus("发现 " + jsUrls.size() + " 个JS文件，开始分析...");
                        if (jsUrls.size() > 0) {
                            progressBar.setValue(50);
                            progressBar.setString("开始分析JS文件...");
                        }
                    });

                    // 分析JS文件（这个方法内部会更新进度）
                    if (jsUrls.size() > 0) {
                        analyzeJavaScriptFiles(jsUrls);
                    } else {
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setValue(100);
                            progressBar.setString("未发现JS文件");
                        });
                    }
                } else {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(100);
                        progressBar.setString("主页面分析完成");
                    });
                }

                // 最终更新显示
                SwingUtilities.invokeLater(() -> {
                    // 确保Host过滤器是最新的
                    updateHostFilter();
                    updateTableDisplay();
                    updateStatus("✓ 分析完成！共发现 " + foundItems + " 项敏感信息，发现 " + discoveredHosts.size() + " 个Host");
                });

            } else {
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(0);
                    progressBar.setString("获取页面失败");
                    updateStatus("✗ 获取页面失败: " + targetUrl);
                });
            }

        } catch (Exception e) {
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(0);
                progressBar.setString("分析失败");
                updateStatus("✗ 分析过程中发生错误: " + e.getMessage());
            });
            callbacks.printError("Analysis error: " + e.getMessage());
        }
    }

    /**
     * 获取页面内容 - 优化版，支持大文件和超时控制
     */
    private String getPageContent(String url) {
        return getPageContent(url, 30000); // 默认30秒超时
    }

    /**
     * 获取页面内容 - 带超时控制和大文件优化
     */
    private String getPageContent(String url, int timeoutMs) {
        try {
            URL urlObj = new URL(url);

            // 添加超时和大文件处理的HTTP头
            byte[] request = buildOptimizedHttpRequest(urlObj);

            // 创建IHttpService对象
            IHttpService httpService = helpers.buildHttpService(
                urlObj.getHost(),
                urlObj.getPort() == -1 ? (urlObj.getProtocol().equals("https") ? 443 : 80) : urlObj.getPort(),
                urlObj.getProtocol()
            );

            // 使用超时控制的请求
            IHttpRequestResponse response = makeHttpRequestWithTimeout(httpService, request, timeoutMs);

            if (response != null && response.getResponse() != null) {
                byte[] responseBytes = response.getResponse();

                // 检查响应大小，对大文件进行特殊处理
                if (responseBytes.length > 1024 * 1024) { // 大于1MB
                    SwingUtilities.invokeLater(() -> {
                        updateStatus("处理大文件: " + (responseBytes.length / 1024) + "KB");
                    });

                    // 对大文件使用流式处理
                    return processLargeFile(responseBytes, url);
                } else {
                    return new String(responseBytes);
                }
            }
        } catch (Exception e) {
            callbacks.printError("Failed to get page content: " + e.getMessage());
            SwingUtilities.invokeLater(() -> {
                updateStatus("获取失败: " + url + " - " + e.getMessage());
            });
        }
        return null;
    }

    /**
     * 构建优化的HTTP请求
     */
    private byte[] buildOptimizedHttpRequest(URL url) {
        try {
            // 基础请求
            byte[] baseRequest = helpers.buildHttpRequest(url);
            String requestStr = new String(baseRequest);

            // 添加优化头部
            StringBuilder optimizedRequest = new StringBuilder();
            String[] lines = requestStr.split("\r\n");

            // 添加请求行
            optimizedRequest.append(lines[0]).append("\r\n");

            // 添加基础头部
            boolean hasUserAgent = false;
            boolean hasAcceptEncoding = false;
            boolean hasConnection = false;

            for (int i = 1; i < lines.length; i++) {
                String line = lines[i];
                if (line.toLowerCase().startsWith("user-agent:")) {
                    hasUserAgent = true;
                } else if (line.toLowerCase().startsWith("accept-encoding:")) {
                    hasAcceptEncoding = true;
                } else if (line.toLowerCase().startsWith("connection:")) {
                    hasConnection = true;
                }
                optimizedRequest.append(line).append("\r\n");
            }

            // 添加优化头部
            if (!hasUserAgent) {
                optimizedRequest.append("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\r\n");
            }
            if (!hasAcceptEncoding) {
                optimizedRequest.append("Accept-Encoding: gzip, deflate\r\n");
            }
            if (!hasConnection) {
                optimizedRequest.append("Connection: keep-alive\r\n");
            }

            // 添加缓存控制
            optimizedRequest.append("Cache-Control: no-cache\r\n");
            optimizedRequest.append("Pragma: no-cache\r\n");

            optimizedRequest.append("\r\n");

            return optimizedRequest.toString().getBytes();
        } catch (Exception e) {
            // 如果优化失败，返回基础请求
            return helpers.buildHttpRequest(url);
        }
    }

    /**
     * 带超时控制的HTTP请求
     */
    private IHttpRequestResponse makeHttpRequestWithTimeout(IHttpService httpService, byte[] request, int timeoutMs) {
        try {
            // 使用Future来实现超时控制
            java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newSingleThreadExecutor();
            java.util.concurrent.Future<IHttpRequestResponse> future = executor.submit(() -> {
                return callbacks.makeHttpRequest(httpService, request);
            });

            try {
                return future.get(timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            } catch (java.util.concurrent.TimeoutException e) {
                future.cancel(true);
                callbacks.printError("Request timeout after " + timeoutMs + "ms");
                return null;
            } finally {
                executor.shutdown();
            }
        } catch (Exception e) {
            callbacks.printError("Request failed: " + e.getMessage());
            return null;
        }
    }

    /**
     * 处理大文件 - 修复版，正确处理状态和进度
     */
    private String processLargeFile(byte[] responseBytes, String url) {
        try {
            String content = new String(responseBytes);
            int originalSize = content.length();

            // 对于大文件，我们可以选择只分析前面的部分
            if (originalSize > 500000) { // 大于500KB
                SwingUtilities.invokeLater(() -> {
                    updateStatus("处理大文件 (" + (originalSize / 1024) + "KB): " + getFileName(url) + " - 截取前500KB分析");
                });

                // 只分析前500KB的内容，通常敏感信息在文件开头
                content = content.substring(0, 500000);
                content += "\n\n/* === 原文件大小: " + (originalSize / 1024) + "KB，已截取前500KB进行分析 === */";

                // 记录大文件处理信息
                callbacks.printOutput("Large file processed: " + url +
                    " (Original: " + (originalSize / 1024) + "KB, Analyzed: 500KB)");
            } else {
                SwingUtilities.invokeLater(() -> {
                    updateStatus("处理文件 (" + (originalSize / 1024) + "KB): " + getFileName(url));
                });
            }

            return content;
        } catch (Exception e) {
            callbacks.printError("Failed to process large file: " + e.getMessage());
            SwingUtilities.invokeLater(() -> {
                updateStatus("大文件处理失败: " + getFileName(url) + " - " + e.getMessage());
            });
            return null;
        }
    }

    /**
     * 分析内容中的敏感信息
     */
    private void analyzeContent(String content, String source, String url) {
        // 提取并记录host信息 - 优化版，减少UI更新频率
        String host = extractHost(url);
        boolean shouldUpdateUI = false;

        synchronized (discoveredHosts) {
            if (!host.equals("未知Host") && !host.equals("本地") && !host.trim().isEmpty()) {
                boolean isNewHost = discoveredHosts.add(host);
                if (isNewHost) {
                    shouldUpdateUI = true;
                    callbacks.printOutput("发现新Host: " + host + " (总计: " + discoveredHosts.size() + "个)");
                }
            }
        }

        // 延迟更新UI，避免频繁更新
        if (shouldUpdateUI) {
            SwingUtilities.invokeLater(() -> {
                updateHostFilter();
            });
        }

        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            Matcher matcher = pattern.pattern.matcher(content);
            while (matcher.find()) {
                String match = matcher.group().trim();

                // 过滤明显的示例数据
                if (isExampleData(match)) {
                    continue;
                }

                SensitiveDataItem item = new SensitiveDataItem(
                    pattern.name, match, source, url, pattern.description
                );

                // 改进的去重逻辑：检查相同内容和来源的敏感信息
                List<SensitiveDataItem> typeList = sensitiveData.computeIfAbsent(pattern.name, k -> new ArrayList<>());
                if (!isDuplicateSensitiveInfo(typeList, item)) {
                    typeList.add(item);
                    foundItems++;

                    // 向API扫描主界面发送敏感信息
                    if (apiScanMainTab != null) {
                        apiScanMainTab.addSensitiveData(item);
                    }

                    // 输出发现的新敏感信息
                    callbacks.printOutput("发现敏感信息 [" + pattern.name + "]: " +
                                        maskSensitiveValue(match) + " (来源: " + source + ")");
                } else {
                    // 输出去重信息（可选，用于调试）
                    // callbacks.printOutput("跳过重复敏感信息 [" + pattern.name + "]: " +
                    //                     maskSensitiveValue(match) + " (来源: " + source + ")");
                }
            }
        }
        analyzedUrls++;
    }

    /**
     * 检查是否为示例数据
     */
    private boolean isExampleData(String match) {
        String lowerMatch = match.toLowerCase();
        return lowerMatch.contains("example") ||
               lowerMatch.contains("test") ||
               lowerMatch.contains("demo") ||
               lowerMatch.contains("sample") ||
               lowerMatch.contains("placeholder");
    }

    /**
     * 提取JavaScript URLs
     */
    private Set<String> extractJavaScriptUrls(String content, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();

        // 简单的JS URL提取逻辑
        Pattern jsPattern = Pattern.compile("src=[\"']([^\"']*\\.js[^\"']*)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = jsPattern.matcher(content);

        while (matcher.find()) {
            String jsUrl = matcher.group(1);
            try {
                if (jsUrl.startsWith("http")) {
                    jsUrls.add(jsUrl);
                } else {
                    URL base = new URL(baseUrl);
                    URL resolved = new URL(base, jsUrl);
                    jsUrls.add(resolved.toString());
                }
            } catch (Exception e) {
                // 忽略无效URL
            }
        }

        return jsUrls;
    }

    /**
     * 分析JavaScript文件 - 修复版，正确处理状态同步和大文件
     */
    private void analyzeJavaScriptFiles(Set<String> jsUrls) {
        if (jsUrls.isEmpty()) {
            return;
        }

        // 过滤已处理的URL
        Set<String> urlsToProcess = new HashSet<>();
        for (String jsUrl : jsUrls) {
            if (!processedUrls.contains(jsUrl)) {
                urlsToProcess.add(jsUrl);
            }
        }

        if (urlsToProcess.isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                updateStatus("所有JS文件已处理过，跳过重复分析");
            });
            return;
        }

        SwingUtilities.invokeLater(() -> {
            updateStatus("开始分析 " + urlsToProcess.size() + " 个JS文件...");
        });

        // 使用线程池进行并发处理
        java.util.concurrent.ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(
            Math.min(urlsToProcess.size(), 3) // 减少并发数到3，避免过载
        );

        java.util.concurrent.CountDownLatch latch = new java.util.concurrent.CountDownLatch(urlsToProcess.size());
        java.util.concurrent.atomic.AtomicInteger startedCount = new java.util.concurrent.atomic.AtomicInteger(0);
        java.util.concurrent.atomic.AtomicInteger completedCount = new java.util.concurrent.atomic.AtomicInteger(0);
        java.util.concurrent.atomic.AtomicInteger successCount = new java.util.concurrent.atomic.AtomicInteger(0);
        java.util.concurrent.atomic.AtomicInteger failedCount = new java.util.concurrent.atomic.AtomicInteger(0);

        for (String jsUrl : urlsToProcess) {
            executor.submit(() -> {
                String fileName = getFileName(jsUrl);
                java.util.concurrent.atomic.AtomicBoolean success = new java.util.concurrent.atomic.AtomicBoolean(false);

                try {
                    // 开始处理
                    int started = startedCount.incrementAndGet();
                    SwingUtilities.invokeLater(() -> {
                        updateStatus("正在加载JS文件 " + started + "/" + urlsToProcess.size() + ": " + fileName);
                        progressBar.setValue((started * 50) / urlsToProcess.size()); // 加载阶段占50%
                    });

                    // 获取JS内容
                    String jsContent = getPageContent(jsUrl, 20000); // 增加到20秒超时

                    if (jsContent != null && !jsContent.trim().isEmpty()) {
                        // 标记为正在分析
                        SwingUtilities.invokeLater(() -> {
                            updateStatus("正在分析JS文件: " + fileName + " (" + (jsContent.length() / 1024) + "KB)");
                        });

                        // 同步更新处理状态
                        synchronized (this) {
                            totalRequests++;
                            processedUrls.add(jsUrl);
                        }

                        // 分析内容
                        analyzeContent(jsContent, "[JS文件]", jsUrl);
                        success.set(true);
                        successCount.incrementAndGet();

                        SwingUtilities.invokeLater(() -> {
                            updateStatistics();
                        });
                    } else {
                        failedCount.incrementAndGet();
                        SwingUtilities.invokeLater(() -> {
                            updateStatus("JS文件加载失败或为空: " + fileName);
                        });
                    }
                } catch (Exception e) {
                    failedCount.incrementAndGet();
                    callbacks.printError("Failed to analyze JS file " + jsUrl + ": " + e.getMessage());
                    SwingUtilities.invokeLater(() -> {
                        updateStatus("JS文件分析异常: " + fileName + " - " + e.getMessage());
                    });
                } finally {
                    // 完成处理
                    int completed = completedCount.incrementAndGet();
                    SwingUtilities.invokeLater(() -> {
                        String status = success.get() ? "✓" : "✗";
                        updateStatus("JS文件 " + status + " " + completed + "/" + urlsToProcess.size() + " 完成: " + fileName);
                        progressBar.setValue(50 + (completed * 50) / urlsToProcess.size()); // 分析阶段占50%
                    });

                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        try {
            boolean finished = latch.await(120, java.util.concurrent.TimeUnit.SECONDS); // 增加到120秒

            SwingUtilities.invokeLater(() -> {
                if (finished) {
                    updateStatus("JS文件分析完成 - 成功: " + successCount.get() + ", 失败: " + failedCount.get() + ", 总计: " + urlsToProcess.size());
                    progressBar.setValue(100);
                    progressBar.setString("JS文件分析完成");
                } else {
                    updateStatus("JS文件分析超时 - 已完成: " + completedCount.get() + "/" + urlsToProcess.size());
                    progressBar.setString("分析超时，部分完成");
                }

                // 确保Host过滤器在JS分析完成后是最新的
                updateHostFilter();
            });
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            SwingUtilities.invokeLater(() -> {
                updateStatus("JS文件分析被中断");
            });
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, java.util.concurrent.TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 获取文件名（用于显示）
     */
    private String getFileName(String url) {
        try {
            String path = new URL(url).getPath();
            int lastSlash = path.lastIndexOf('/');
            if (lastSlash >= 0 && lastSlash < path.length() - 1) {
                return path.substring(lastSlash + 1);
            }
            return path;
        } catch (Exception e) {
            return url;
        }
    }

    /**
     * 从URL中提取Host信息 - 修复版，更好地处理各种URL格式
     */
    private String extractHost(String url) {
        try {
            if (url == null || url.trim().isEmpty()) {
                return "未知Host";
            }

            String trimmedUrl = url.trim();

            // 处理完整URL
            if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
                URL urlObj = new URL(trimmedUrl);
                String host = urlObj.getHost();

                if (host == null || host.trim().isEmpty()) {
                    return "未知Host";
                }

                int port = urlObj.getPort();

                // 如果端口不是默认端口，则包含端口号
                if (port != -1 && port != 80 && port != 443) {
                    return host + ":" + port;
                }
                return host;
            }

            // 处理协议相对URL
            if (trimmedUrl.startsWith("//")) {
                return extractHost("http:" + trimmedUrl);
            }

            // 处理可能包含host的其他格式
            if (trimmedUrl.contains("://")) {
                try {
                    URL urlObj = new URL(trimmedUrl);
                    String host = urlObj.getHost();
                    if (host != null && !host.trim().isEmpty()) {
                        int port = urlObj.getPort();
                        if (port != -1 && port != 80 && port != 443) {
                            return host + ":" + port;
                        }
                        return host;
                    }
                } catch (Exception e) {
                    // 继续处理其他情况
                }
            }

            // 如果是路径或其他格式，返回"本地"
            return "本地";

        } catch (Exception e) {
            callbacks.printError("提取Host失败: " + url + " - " + e.getMessage());
            return "未知Host";
        }
    }

    /**
     * 更新Host过滤器选项 - 修复版，解决多域名扫描时的空白问题
     */
    private void updateHostFilter() {
        // 在同步块中创建Host列表的副本，避免并发修改异常
        List<String> hostList;
        synchronized (discoveredHosts) {
            hostList = new ArrayList<>(discoveredHosts);
        }

        // 排序Host列表，提供更好的用户体验
        Collections.sort(hostList);

        SwingUtilities.invokeLater(() -> {
            try {
                String currentSelection = (String) hostFilterComboBox.getSelectedItem();

                // 临时移除监听器，避免在更新过程中触发过滤
                ActionListener[] listeners = hostFilterComboBox.getActionListeners();
                for (ActionListener listener : listeners) {
                    hostFilterComboBox.removeActionListener(listener);
                }

                // 清空并重新添加选项
                hostFilterComboBox.removeAllItems();
                hostFilterComboBox.addItem("全部Host");

                // 添加所有发现的host
                for (String host : hostList) {
                    if (host != null && !host.trim().isEmpty()) {
                        hostFilterComboBox.addItem(host);
                    }
                }

                // 尝试恢复之前的选择
                if (currentSelection != null && hostList.contains(currentSelection)) {
                    hostFilterComboBox.setSelectedItem(currentSelection);
                } else if (hostFilterComboBox.getItemCount() > 1) {
                    // 如果之前的选择不存在，但有Host可选，保持"全部Host"选中
                    hostFilterComboBox.setSelectedIndex(0);
                }

                // 重新添加监听器
                for (ActionListener listener : listeners) {
                    hostFilterComboBox.addActionListener(listener);
                }

                // 调试信息
                callbacks.printOutput("Host过滤器更新完成，当前Host数量: " + hostList.size() +
                    ", 选中项: " + hostFilterComboBox.getSelectedItem() +
                    ", Host列表: " + hostList);

            } catch (Exception e) {
                callbacks.printError("更新Host过滤器时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 检查是否为重复的敏感信息
     * 基于内容和来源进行去重判断
     */
    private boolean isDuplicateSensitiveInfo(List<SensitiveDataItem> existingItems, SensitiveDataItem newItem) {
        for (SensitiveDataItem existing : existingItems) {
            // 检查内容和来源是否完全相同
            if (existing.value.equals(newItem.value) && existing.source.equals(newItem.source)) {
                return true;
            }

            // 对于URL来源，还要检查URL是否相同
            if (existing.url.equals(newItem.url) && existing.value.equals(newItem.value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 掩码敏感信息值用于安全显示
     */
    private String maskSensitiveValue(String value) {
        if (value == null || value.length() <= 6) {
            return "***";
        }

        // 显示前3个和后3个字符，中间用*代替
        return value.substring(0, 3) + "***" + value.substring(value.length() - 3);
    }

    public String getTabCaption() {
        return "敏感信息提取";
    }

    public JPanel getPanel() {
        return mainPanel;
    }
}
