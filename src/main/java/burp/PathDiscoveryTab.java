package burp;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 路径发现Tab - 参考JsRouteScan的PathTab设计
 * 提供API路径的发现、管理和分析功能
 */
public class PathDiscoveryTab {
    
    private IBurpExtenderCallbacks callbacks;
    private ApiScanMainTab mainTab;
    
    // UI组件
    private JPanel mainPanel;
    private JSplitPane splitPane;
    private JTable pathTable;
    private DefaultTableModel tableModel;
    private TableRowSorter<DefaultTableModel> sorter;
    private JTextArea detailArea;
    private JLabel statusLabel;
    private JComboBox<String> hostFilterCombo;
    private JComboBox<String> typeFilterCombo;

    // 搜索功能组件
    private JTextField searchField;
    private JComboBox<String> searchTypeCombo;
    private JButton clearSearchButton;
    
    // 数据
    private List<RouteContent> routeList;
    
    // 表格列定义
    private static final String[] COLUMN_NAMES = {
        "选择", "主机", "路径", "类型", "方法", "参数", "风险", "状态", "来源", "发现时间"
    };
    
    public PathDiscoveryTab(IBurpExtenderCallbacks callbacks, ApiScanMainTab mainTab) {
        this.callbacks = callbacks;
        this.mainTab = mainTab;
        this.routeList = new ArrayList<>();
        
        initializeUI();
    }
    
    /**
     * 初始化用户界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建顶部工具栏
        JPanel toolbarPanel = createToolbar();
        mainPanel.add(toolbarPanel, BorderLayout.NORTH);
        
        // 创建主要内容区域
        splitPane = createMainContent();
        mainPanel.add(splitPane, BorderLayout.CENTER);
        
        // 创建底部状态栏
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建工具栏
     */
    private JPanel createToolbar() {
        JPanel toolbar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        toolbar.setBorder(BorderFactory.createEtchedBorder());

        // 搜索功能区域
        toolbar.add(new JLabel("🔍 搜索:"));
        searchField = new JTextField(15);
        searchField.setToolTipText("输入关键词搜索路径、主机或参数");
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            public void insertUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void removeUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
            public void changedUpdate(javax.swing.event.DocumentEvent e) { performSearch(); }
        });
        toolbar.add(searchField);

        // 搜索类型选择
        searchTypeCombo = new JComboBox<>(new String[]{
            "全部字段", "路径", "主机", "类型", "方法", "参数", "来源"
        });
        searchTypeCombo.setToolTipText("选择搜索范围");
        searchTypeCombo.addActionListener(e -> performSearch());
        toolbar.add(searchTypeCombo);

        // 清除搜索按钮
        clearSearchButton = new JButton("✖");
        clearSearchButton.setToolTipText("清除搜索条件");
        clearSearchButton.addActionListener(e -> clearSearch());
        toolbar.add(clearSearchButton);

        toolbar.add(new JSeparator(SwingConstants.VERTICAL));

        // 主机过滤
        toolbar.add(new JLabel("主机:"));
        hostFilterCombo = new JComboBox<>();
        hostFilterCombo.addItem("全部主机");
        hostFilterCombo.addActionListener(e -> filterPaths());
        toolbar.add(hostFilterCombo);
        
        // 类型过滤
        toolbar.add(new JLabel("类型:"));
        typeFilterCombo = new JComboBox<>();
        typeFilterCombo.addItem("全部类型");
        typeFilterCombo.addItem("API");
        typeFilterCombo.addItem("管理");
        typeFilterCombo.addItem("认证");
        typeFilterCombo.addItem("数据");
        typeFilterCombo.addItem("文件");
        typeFilterCombo.addItem("页面");
        typeFilterCombo.addActionListener(e -> filterPaths());
        toolbar.add(typeFilterCombo);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 全选按钮
        JButton selectAllButton = new JButton("全选");
        selectAllButton.addActionListener(e -> selectAllPaths(true));
        toolbar.add(selectAllButton);
        
        // 取消全选按钮
        JButton deselectAllButton = new JButton("取消全选");
        deselectAllButton.addActionListener(e -> selectAllPaths(false));
        toolbar.add(deselectAllButton);
        
        toolbar.add(new JSeparator(SwingConstants.VERTICAL));
        
        // 测试选中按钮
        JButton testSelectedButton = new JButton("🧪 测试选中");
        testSelectedButton.addActionListener(e -> testSelectedPaths());
        toolbar.add(testSelectedButton);
        
        // 发送到Repeater按钮
        JButton sendToRepeaterButton = new JButton("📤 发送到Repeater");
        sendToRepeaterButton.addActionListener(e -> sendToRepeater());
        toolbar.add(sendToRepeaterButton);
        
        // 刷新按钮
        JButton refreshButton = new JButton("🔄 刷新");
        refreshButton.addActionListener(e -> refreshData());
        toolbar.add(refreshButton);
        
        // 清除按钮
        JButton clearButton = new JButton("🗑️ 清除");
        clearButton.addActionListener(e -> clearSelectedPaths());
        toolbar.add(clearButton);
        
        // 导出按钮
        JButton exportButton = new JButton("📤 导出");
        exportButton.addActionListener(e -> exportPathData());
        toolbar.add(exportButton);
        
        return toolbar;
    }
    
    /**
     * 创建主要内容区域
     */
    private JSplitPane createMainContent() {
        // 创建表格
        tableModel = new DefaultTableModel(COLUMN_NAMES, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) {
                    return Boolean.class; // 选择列
                }
                return String.class;
            }
            
            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // 只有选择列可编辑
            }
        };
        
        pathTable = new JTable(tableModel);
        pathTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        pathTable.setAutoCreateRowSorter(true);
        
        // 设置列宽
        pathTable.getColumnModel().getColumn(0).setMaxWidth(50);  // 选择列
        pathTable.getColumnModel().getColumn(3).setMaxWidth(80);  // 类型列
        pathTable.getColumnModel().getColumn(4).setMaxWidth(100); // 方法列
        pathTable.getColumnModel().getColumn(6).setMaxWidth(80);  // 风险列
        pathTable.getColumnModel().getColumn(7).setMaxWidth(80);  // 状态列
        
        // 添加表格选择监听器
        pathTable.getSelectionModel().addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                showPathDetails();
            }
        });
        
        // 添加右键菜单
        addContextMenu();
        
        JScrollPane tableScrollPane = new JScrollPane(pathTable);
        tableScrollPane.setPreferredSize(new Dimension(800, 400));
        
        // 创建详情面板
        detailArea = new JTextArea();
        detailArea.setEditable(false);
        detailArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        detailArea.setText("选择路径以查看详细信息...");
        
        JScrollPane detailScrollPane = new JScrollPane(detailArea);
        detailScrollPane.setPreferredSize(new Dimension(800, 200));
        detailScrollPane.setBorder(BorderFactory.createTitledBorder("路径详情"));
        
        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, tableScrollPane, detailScrollPane);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.7);
        
        return splitPane;
    }
    
    /**
     * 创建状态栏
     */
    private JPanel createStatusPanel() {
        JPanel statusPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        statusPanel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        statusLabel = new JLabel("路径总数: 0 | 选中: 0 | API端点: 0 | 已测试: 0");
        statusPanel.add(statusLabel);
        
        return statusPanel;
    }
    
    /**
     * 添加右键菜单
     */
    private void addContextMenu() {
        JPopupMenu contextMenu = new JPopupMenu();
        
        JMenuItem viewDetailsItem = new JMenuItem("查看详情");
        viewDetailsItem.addActionListener(e -> showPathDetails());
        contextMenu.add(viewDetailsItem);
        
        JMenuItem copyUrlItem = new JMenuItem("复制URL");
        copyUrlItem.addActionListener(e -> copySelectedUrl());
        contextMenu.add(copyUrlItem);
        
        JMenuItem copyPathItem = new JMenuItem("复制路径");
        copyPathItem.addActionListener(e -> copySelectedPath());
        contextMenu.add(copyPathItem);
        
        contextMenu.addSeparator();
        
        JMenuItem testPathItem = new JMenuItem("测试路径");
        testPathItem.addActionListener(e -> testSelectedPath());
        contextMenu.add(testPathItem);
        
        JMenuItem sendToRepeaterItem = new JMenuItem("发送到Repeater");
        sendToRepeaterItem.addActionListener(e -> sendToRepeater());
        contextMenu.add(sendToRepeaterItem);
        
        contextMenu.addSeparator();
        
        JMenuItem removePathItem = new JMenuItem("移除路径");
        removePathItem.addActionListener(e -> removeSelectedPath());
        contextMenu.add(removePathItem);
        
        pathTable.setComponentPopupMenu(contextMenu);
    }
    
    /**
     * 添加路径
     */
    public void addRoute(RouteContent route) {
        if (!routeList.contains(route)) {
            routeList.add(route);
            updateTableRow(route);
            updateHostFilter();
            updateStatusLabel();
        }
    }
    
    /**
     * 更新表格行
     */
    private void updateTableRow(RouteContent route) {
        SwingUtilities.invokeLater(() -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
            String discoveryTime = route.getDiscoveryTime().format(formatter);
            
            Object[] rowData = {
                false, // 选择状态
                route.getHost(),
                route.getPath(),
                route.getPathType(),
                route.getMethodsString(),
                route.getParametersString(),
                route.getRiskLevelDescription(),
                route.getStatusDescription(),
                route.getSourceType() != null ? route.getSourceType() : "未知",
                discoveryTime
            };
            
            tableModel.addRow(rowData);
        });
    }
    
    /**
     * 更新主机过滤器
     */
    private void updateHostFilter() {
        SwingUtilities.invokeLater(() -> {
            String currentSelection = (String) hostFilterCombo.getSelectedItem();
            hostFilterCombo.removeAllItems();
            hostFilterCombo.addItem("全部主机");
            
            routeList.stream()
                .map(RouteContent::getHost)
                .distinct()
                .sorted()
                .forEach(hostFilterCombo::addItem);
            
            if (currentSelection != null) {
                hostFilterCombo.setSelectedItem(currentSelection);
            }
        });
    }
    
    /**
     * 过滤路径（考虑搜索条件）
     */
    private void filterPaths() {
        String selectedHost = (String) hostFilterCombo.getSelectedItem();
        String selectedType = (String) typeFilterCombo.getSelectedItem();
        String searchText = searchField != null ? searchField.getText().trim().toLowerCase() : "";
        String searchType = searchTypeCombo != null ? (String) searchTypeCombo.getSelectedItem() : "全部字段";

        // 如果没有任何过滤条件，清除所有过滤器
        if ("全部主机".equals(selectedHost) && "全部类型".equals(selectedType) && searchText.isEmpty()) {
            pathTable.setRowSorter(null);
            updateStatusLabel();
            return;
        }

        // 创建综合过滤器
        RowFilter<DefaultTableModel, Object> combinedFilter = new RowFilter<DefaultTableModel, Object>() {
            @Override
            public boolean include(Entry<? extends DefaultTableModel, ? extends Object> entry) {
                // 主机过滤
                boolean hostMatch = "全部主机".equals(selectedHost) ||
                    selectedHost.equals(entry.getStringValue(1));

                // 类型过滤
                boolean typeMatch = "全部类型".equals(selectedType) ||
                    selectedType.equals(entry.getStringValue(3));

                // 搜索过滤
                boolean searchMatch = true;
                if (!searchText.isEmpty()) {
                    switch (searchType) {
                        case "路径":
                            searchMatch = entry.getStringValue(2).toLowerCase().contains(searchText);
                            break;
                        case "主机":
                            searchMatch = entry.getStringValue(1).toLowerCase().contains(searchText);
                            break;
                        case "类型":
                            searchMatch = entry.getStringValue(3).toLowerCase().contains(searchText);
                            break;
                        case "方法":
                            searchMatch = entry.getStringValue(4).toLowerCase().contains(searchText);
                            break;
                        case "参数":
                            searchMatch = entry.getStringValue(5).toLowerCase().contains(searchText);
                            break;
                        case "来源":
                            searchMatch = entry.getStringValue(8).toLowerCase().contains(searchText);
                            break;
                        case "全部字段":
                        default:
                            searchMatch = entry.getStringValue(1).toLowerCase().contains(searchText) || // 主机
                                         entry.getStringValue(2).toLowerCase().contains(searchText) || // 路径
                                         entry.getStringValue(3).toLowerCase().contains(searchText) || // 类型
                                         entry.getStringValue(4).toLowerCase().contains(searchText) || // 方法
                                         entry.getStringValue(5).toLowerCase().contains(searchText) || // 参数
                                         entry.getStringValue(8).toLowerCase().contains(searchText);   // 来源
                            break;
                    }
                }

                return hostMatch && typeMatch && searchMatch;
            }
        };

        // 应用过滤器
        if (sorter == null) {
            sorter = new TableRowSorter<>(tableModel);
        }
        sorter.setRowFilter(combinedFilter);
        pathTable.setRowSorter(sorter);

        updateStatusLabel();
    }
    
    /**
     * 显示路径详情
     */
    private void showPathDetails() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = pathTable.convertRowIndexToModel(selectedRow);
            String host = (String) tableModel.getValueAt(modelRow, 1);
            String path = (String) tableModel.getValueAt(modelRow, 2);
            
            RouteContent route = findRoute(host, path);
            if (route != null) {
                displayRouteDetails(route);
            }
        }
    }
    
    /**
     * 显示路径详细信息
     */
    private void displayRouteDetails(RouteContent route) {
        StringBuilder details = new StringBuilder();
        details.append("路径信息详情\n");
        for (int i = 0; i < 50; i++) details.append("=");
        details.append("\n\n");
        
        details.append("基本信息:\n");
        details.append("  完整URL: ").append(route.getFullUrl()).append("\n");
        details.append("  主机: ").append(route.getHost()).append("\n");
        details.append("  路径: ").append(route.getPath()).append("\n");
        details.append("  类型: ").append(route.getPathType()).append("\n");
        details.append("  风险等级: ").append(route.getRiskLevelDescription()).append(" (").append(route.getRiskLevel()).append("/5)\n\n");
        
        details.append("HTTP信息:\n");
        details.append("  支持方法: ").append(route.getMethodsString()).append("\n");
        details.append("  参数列表: ").append(route.getParametersString()).append("\n");
        details.append("  状态码: ").append(route.getStatusCode() > 0 ? route.getStatusCode() : "未测试").append("\n");
        details.append("  内容类型: ").append(route.getContentType() != null ? route.getContentType() : "未知").append("\n");
        details.append("  响应时间: ").append(route.getResponseTime() > 0 ? route.getResponseTime() + "ms" : "未测试").append("\n\n");
        
        details.append("发现信息:\n");
        details.append("  来源文件: ").append(route.getSourceFile() != null ? route.getSourceFile() : "未知").append("\n");
        details.append("  来源类型: ").append(route.getSourceType() != null ? route.getSourceType() : "未知").append("\n");
        details.append("  发现时间: ").append(route.getDiscoveryTime()).append("\n");
        details.append("  是否已测试: ").append(route.isTested() ? "是" : "否").append("\n\n");
        
        details.append("特征分析:\n");
        details.append("  是否API端点: ").append(route.isApiEndpoint() ? "是" : "否").append("\n");
        details.append("  是否有参数: ").append(route.isHasParameters() ? "是" : "否").append("\n");
        details.append("  是否安全路径: ").append(route.isSecure() ? "是" : "否").append("\n\n");
        
        if (route.isTested() && route.getResponseBody() != null) {
            details.append("响应内容 (前500字符):\n");
            String responseBody = route.getResponseBody();
            if (responseBody.length() > 500) {
                details.append(responseBody.substring(0, 500)).append("...\n");
            } else {
                details.append(responseBody).append("\n");
            }
        }
        
        detailArea.setText(details.toString());
        detailArea.setCaretPosition(0);
    }
    
    /**
     * 根据主机和路径查找路由
     */
    private RouteContent findRoute(String host, String path) {
        return routeList.stream()
            .filter(route -> route.getHost().equals(host) && route.getPath().equals(path))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 全选/取消全选路径
     */
    private void selectAllPaths(boolean select) {
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            tableModel.setValueAt(select, i, 0);
        }
        updateStatusLabel();
    }
    
    /**
     * 复制选中的URL
     */
    private void copySelectedUrl() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = pathTable.convertRowIndexToModel(selectedRow);
            String host = (String) tableModel.getValueAt(modelRow, 1);
            String path = (String) tableModel.getValueAt(modelRow, 2);
            
            RouteContent route = findRoute(host, path);
            if (route != null) {
                String url = route.getFullUrl();
                Toolkit.getDefaultToolkit().getSystemClipboard()
                    .setContents(new java.awt.datatransfer.StringSelection(url), null);
                callbacks.printOutput("已复制URL: " + url);
            }
        }
    }
    
    /**
     * 复制选中的路径
     */
    private void copySelectedPath() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = pathTable.convertRowIndexToModel(selectedRow);
            String path = (String) tableModel.getValueAt(modelRow, 2);
            
            Toolkit.getDefaultToolkit().getSystemClipboard()
                .setContents(new java.awt.datatransfer.StringSelection(path), null);
            callbacks.printOutput("已复制路径: " + path);
        }
    }
    
    /**
     * 测试选中的路径
     */
    private void testSelectedPath() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = pathTable.convertRowIndexToModel(selectedRow);
            String host = (String) tableModel.getValueAt(modelRow, 1);
            String path = (String) tableModel.getValueAt(modelRow, 2);
            
            RouteContent route = findRoute(host, path);
            if (route != null) {
                callbacks.printOutput("开始测试路径: " + route.getFullUrl());
                // TODO: 实现单个路径测试逻辑
            }
        }
    }
    
    /**
     * 测试选中的路径
     */
    private void testSelectedPaths() {
        List<RouteContent> selectedRoutes = getSelectedRoutes();
        if (selectedRoutes.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要测试的路径");
            return;
        }
        
        callbacks.printOutput("开始批量测试 " + selectedRoutes.size() + " 个路径");
        // TODO: 实现批量路径测试逻辑
    }
    
    /**
     * 发送到Repeater
     */
    private void sendToRepeater() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int modelRow = pathTable.convertRowIndexToModel(selectedRow);
            String host = (String) tableModel.getValueAt(modelRow, 1);
            String path = (String) tableModel.getValueAt(modelRow, 2);
            
            RouteContent route = findRoute(host, path);
            if (route != null) {
                callbacks.printOutput("发送到Repeater: " + route.getFullUrl());
                // TODO: 实现发送到Repeater的逻辑
            }
        }
    }
    
    /**
     * 移除选中的路径
     */
    private void removeSelectedPath() {
        int selectedRow = pathTable.getSelectedRow();
        if (selectedRow >= 0) {
            int result = JOptionPane.showConfirmDialog(
                mainPanel,
                "确定要移除选中的路径吗？",
                "确认移除",
                JOptionPane.YES_NO_OPTION
            );
            
            if (result == JOptionPane.YES_OPTION) {
                int modelRow = pathTable.convertRowIndexToModel(selectedRow);
                String host = (String) tableModel.getValueAt(modelRow, 1);
                String path = (String) tableModel.getValueAt(modelRow, 2);
                
                // 从数据中移除
                routeList.removeIf(route -> route.getHost().equals(host) && route.getPath().equals(path));
                
                // 从表格中移除
                tableModel.removeRow(modelRow);
                
                updateStatusLabel();
                callbacks.printOutput("已移除路径: " + host + path);
            }
        }
    }
    
    /**
     * 清除选中的路径
     */
    private void clearSelectedPaths() {
        List<Integer> selectedRows = new ArrayList<>();
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                selectedRows.add(i);
            }
        }
        
        if (selectedRows.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请先选择要清除的路径");
            return;
        }
        
        int result = JOptionPane.showConfirmDialog(
            mainPanel,
            "确定要清除 " + selectedRows.size() + " 个选中的路径吗？",
            "确认清除",
            JOptionPane.YES_NO_OPTION
        );
        
        if (result == JOptionPane.YES_OPTION) {
            // 从后往前删除，避免索引变化
            for (int i = selectedRows.size() - 1; i >= 0; i--) {
                int row = selectedRows.get(i);
                String host = (String) tableModel.getValueAt(row, 1);
                String path = (String) tableModel.getValueAt(row, 2);
                
                // 从数据中移除
                routeList.removeIf(route -> route.getHost().equals(host) && route.getPath().equals(path));
                
                // 从表格中移除
                tableModel.removeRow(row);
            }
            
            updateStatusLabel();
            callbacks.printOutput("已清除 " + selectedRows.size() + " 个路径");
        }
    }
    
    /**
     * 导出路径数据
     */
    private void exportPathData() {
        // TODO: 实现路径数据导出功能
        callbacks.printOutput("导出路径数据功能待实现");
    }
    
    /**
     * 获取选中的路由
     */
    private List<RouteContent> getSelectedRoutes() {
        List<RouteContent> selectedRoutes = new ArrayList<>();
        for (int i = 0; i < tableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                String host = (String) tableModel.getValueAt(i, 1);
                String path = (String) tableModel.getValueAt(i, 2);
                RouteContent route = findRoute(host, path);
                if (route != null) {
                    selectedRoutes.add(route);
                }
            }
        }
        return selectedRoutes;
    }
    
    /**
     * 更新状态标签
     */
    private void updateStatusLabel() {
        SwingUtilities.invokeLater(() -> {
            int totalPaths = routeList.size();
            int apiEndpoints = (int) routeList.stream().filter(RouteContent::isApiEndpoint).count();
            int testedPaths = (int) routeList.stream().filter(RouteContent::isTested).count();
            
            int selectedCount = 0;
            for (int i = 0; i < tableModel.getRowCount(); i++) {
                Boolean selected = (Boolean) tableModel.getValueAt(i, 0);
                if (selected != null && selected) {
                    selectedCount++;
                }
            }
            
            statusLabel.setText(String.format("路径总数: %d | 选中: %d | API端点: %d | 已测试: %d", 
                totalPaths, selectedCount, apiEndpoints, testedPaths));
        });
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            for (RouteContent route : routeList) {
                updateTableRow(route);
            }
            updateHostFilter();
            updateStatusLabel();
        });
    }
    
    /**
     * 清除数据
     */
    public void clearData() {
        routeList.clear();
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);
            detailArea.setText("选择路径以查看详细信息...");
            updateHostFilter();
            updateStatusLabel();
        });
    }
    
    /**
     * 执行搜索（委托给filterPaths处理）
     */
    private void performSearch() {
        filterPaths();
    }

    /**
     * 清除搜索条件
     */
    private void clearSearch() {
        searchField.setText("");
        searchTypeCombo.setSelectedIndex(0); // 选择"全部字段"

        // 重新应用过滤器（不包含搜索条件）
        filterPaths();
    }

    /**
     * 获取面板
     */
    public JPanel getPanel() {
        return mainPanel;
    }
}
