package burp;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.StringSelection;
import java.awt.datatransfer.Clipboard;
import java.io.PrintWriter;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 敏感信息提取器 - 基于BurpAPIFinder的设计思路
 * 支持多种敏感信息类型的检测和提取
 */
public class SensitiveInfoExtractor {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    
    // UI组件
    private JPanel mainPanel;
    private JTextField urlField;
    private JButton analyzeButton;
    private JCheckBox autoAnalyzeJSCheckBox;
    private JCheckBox realTimeAnalysisCheckBox;
    private JTextArea outputArea;
    private JProgressBar progressBar;
    private JLabel statusLabel;
    
    // 数据存储
    private Map<String, List<String>> sensitiveData = new HashMap<>();
    private Map<String, String> sensitiveDataSources = new HashMap<>();
    private Set<String> processedUrls = new HashSet<>();
    
    // 敏感信息检测规则 - 基于真实项目的规则库
    private static final SensitivePattern[] SENSITIVE_PATTERNS = {
        // 身份信息
        new SensitivePattern("身份证号", 
            "\\b([1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])\\b", 
            "中国大陆身份证号码"),
        
        new SensitivePattern("手机号码", 
            "\\b(1[3-9]\\d{9})\\b", 
            "中国大陆手机号码"),
        
        new SensitivePattern("邮箱地址", 
            "\\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})\\b", 
            "电子邮箱地址"),
        
        // 网络信息
        new SensitivePattern("IPv4地址", 
            "\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b", 
            "IPv4网络地址"),
        
        new SensitivePattern("内网IP", 
            "\\b(?:10\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|172\\.(?:1[6-9]|2[0-9]|3[0-1])\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|192\\.168\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\\b", 
            "内网IP地址"),
        
        // 认证凭据
        new SensitivePattern("JWT Token", 
            "\\b(eyJ[A-Za-z0-9_/+\\-]{10,}\\.[A-Za-z0-9_/+\\-]{15,}\\.[A-Za-z0-9_/+\\-]{10,})\\b", 
            "JSON Web Token"),
        
        new SensitivePattern("Bearer Token", 
            "\\b[Bb]earer\\s+([a-zA-Z0-9\\-=._+/\\\\]{20,500})\\b", 
            "Bearer认证令牌"),
        
        new SensitivePattern("Basic Auth", 
            "\\b[Bb]asic\\s+([A-Za-z0-9+/]{18,}={0,2})\\b", 
            "Basic认证凭据"),
        
        // 云服务密钥
        new SensitivePattern("阿里云AccessKey", 
            "\\b(LTAI[A-Za-z\\d]{12,30})\\b", 
            "阿里云AccessKey ID"),
        
        new SensitivePattern("腾讯云SecretKey", 
            "\\b(AKID[A-Za-z\\d]{13,40})\\b", 
            "腾讯云AccessKey ID"),
        
        new SensitivePattern("AWS AccessKey", 
            "\\b((?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16})\\b", 
            "AWS AccessKey ID"),
        
        new SensitivePattern("GitHub Token", 
            "\\b((?:ghp|gho|ghu|ghs|ghr|github_pat)_[a-zA-Z0-9_]{36,255})\\b", 
            "GitHub访问令牌"),
        
        new SensitivePattern("Google API Key", 
            "\\b(AIza[0-9A-Za-z_\\-]{35})\\b", 
            "Google API密钥"),
        
        // 数据库和配置
        new SensitivePattern("JDBC连接", 
            "\\b(jdbc:[a-z:]+://[a-z0-9\\.\\-_:;=/@?,&]+)\\b", 
            "数据库JDBC连接字符串"),
        
        new SensitivePattern("私钥", 
            "-----\\s*?BEGIN[ A-Z0-9_\\-]*?PRIVATE KEY\\s*?-----[a-zA-Z0-9\\/\\n\\r=+]*-----\\s*?END[ A-Z0-9_\\-]*? PRIVATE KEY\\s*?-----", 
            "RSA/SSH私钥"),
        
        // Webhook URLs
        new SensitivePattern("企业微信Webhook", 
            "\\b(https://qyapi\\.weixin\\.qq\\.com/cgi-bin/webhook/send\\?key=[a-zA-Z0-9\\-]{25,50})\\b", 
            "企业微信机器人Webhook"),
        
        new SensitivePattern("钉钉Webhook", 
            "\\b(https://oapi\\.dingtalk\\.com/robot/send\\?access_token=[a-z0-9]{50,80})\\b", 
            "钉钉机器人Webhook"),
        
        new SensitivePattern("飞书Webhook", 
            "\\b(https://open\\.feishu\\.cn/open-apis/bot/v2/hook/[a-z0-9\\-]{25,50})\\b", 
            "飞书机器人Webhook"),
        
        new SensitivePattern("Slack Webhook", 
            "\\b(https://hooks\\.slack\\.com/services/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{6,12}/[a-zA-Z0-9\\-_]{15,24})\\b", 
            "Slack机器人Webhook"),
        
        // 密码配置
        new SensitivePattern("密码配置", 
            "(?i)(?:admin_?pass|password|[a-z]{3,15}_?password|user_?pass|user_?pwd|admin_?pwd)[\\s]*[:=][\\s]*[\"']?([a-z0-9!@#$%&*]{6,20})[\"']?", 
            "配置文件中的密码"),
        
        // 微信相关
        new SensitivePattern("微信AppID", 
            "\\b(wx[a-z0-9]{15,18})\\b", 
            "微信公众号/小程序AppID"),
        
        new SensitivePattern("企业微信CorpID", 
            "\\b(ww[a-z0-9]{15,18})\\b", 
            "企业微信CorpID")
    };
    
    // 敏感信息模式类
    private static class SensitivePattern {
        final String name;
        final Pattern pattern;
        final String description;
        
        SensitivePattern(String name, String regex, String description) {
            this.name = name;
            this.pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
            this.description = description;
        }
    }
    
    public SensitiveInfoExtractor(IBurpExtenderCallbacks callbacks, IExtensionHelpers helpers) {
        this.callbacks = callbacks;
        this.helpers = helpers;
        initializeUI();
        initializeData();
    }
    
    /**
     * 初始化UI界面
     */
    private void initializeUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // 创建配置面板
        JPanel configPanel = createConfigPanel();
        mainPanel.add(configPanel, BorderLayout.NORTH);
        
        // 创建结果显示面板
        JPanel resultPanel = createResultPanel();
        mainPanel.add(resultPanel, BorderLayout.CENTER);
        
        // 创建状态面板
        JPanel statusPanel = createStatusPanel();
        mainPanel.add(statusPanel, BorderLayout.SOUTH);
        
        // 显示初始说明
        showWelcomeMessage();
    }
    
    /**
     * 创建配置面板
     */
    private JPanel createConfigPanel() {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(BorderFactory.createTitledBorder("敏感信息提取配置"));
        
        // URL输入区域
        JPanel urlPanel = new JPanel(new BorderLayout(5, 5));
        urlPanel.add(new JLabel("目标URL:"), BorderLayout.WEST);
        
        urlField = new JTextField();
        urlField.setToolTipText("输入要分析的网站URL，支持HTTP/HTTPS");
        urlPanel.add(urlField, BorderLayout.CENTER);
        
        analyzeButton = new JButton("开始分析");
        analyzeButton.addActionListener(e -> startAnalysis());
        urlPanel.add(analyzeButton, BorderLayout.EAST);
        
        // 选项区域
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        autoAnalyzeJSCheckBox = new JCheckBox("自动分析JS文件", true);
        autoAnalyzeJSCheckBox.setToolTipText("自动发现并分析页面中引用的JavaScript文件");
        optionsPanel.add(autoAnalyzeJSCheckBox);
        
        realTimeAnalysisCheckBox = new JCheckBox("实时监控", false);
        realTimeAnalysisCheckBox.setToolTipText("实时监控Burp代理流量中的敏感信息");
        realTimeAnalysisCheckBox.addActionListener(e -> toggleRealTimeAnalysis());
        optionsPanel.add(realTimeAnalysisCheckBox);
        
        panel.add(urlPanel);
        panel.add(Box.createVerticalStrut(5));
        panel.add(optionsPanel);
        
        return panel;
    }
    
    /**
     * 创建结果显示面板
     */
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("分析结果"));
        
        // 输出区域
        outputArea = new JTextArea();
        outputArea.setEditable(false);
        outputArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        outputArea.setBackground(new Color(248, 248, 248));
        
        JScrollPane scrollPane = new JScrollPane(outputArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        scrollPane.setPreferredSize(new Dimension(800, 400));
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 控制按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton clearButton = new JButton("清除结果");
        clearButton.addActionListener(e -> clearResults());
        buttonPanel.add(clearButton);
        
        JButton exportButton = new JButton("导出结果");
        exportButton.addActionListener(e -> exportResults());
        buttonPanel.add(exportButton);
        
        JButton copyButton = new JButton("复制结果");
        copyButton.addActionListener(e -> copyResults());
        buttonPanel.add(copyButton);
        
        JButton saveConfigButton = new JButton("保存配置");
        saveConfigButton.addActionListener(e -> saveConfiguration());
        buttonPanel.add(saveConfigButton);
        
        panel.add(buttonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建状态面板
     */
    private JPanel createStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        statusLabel = new JLabel("就绪");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        panel.add(statusLabel, BorderLayout.WEST);
        
        progressBar = new JProgressBar();
        progressBar.setStringPainted(true);
        progressBar.setString("等待开始...");
        panel.add(progressBar, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 初始化数据结构
     */
    private void initializeData() {
        // 初始化敏感数据存储
        for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
            sensitiveData.put(pattern.name, new ArrayList<>());
        }
    }
    
    /**
     * 显示欢迎信息
     */
    private void showWelcomeMessage() {
        outputArea.append("🔍 敏感信息提取工具\n");
        outputArea.append(createSeparator("=", 50) + "\n");
        outputArea.append("支持检测的敏感信息类型:\n\n");
        
        Map<String, java.util.List<String>> categories = new HashMap<>();
        categories.put("🆔 身份信息", Arrays.asList("身份证号", "手机号码", "邮箱地址"));
        categories.put("🌐 网络信息", Arrays.asList("IPv4地址", "内网IP"));
        categories.put("🔑 认证凭据", Arrays.asList("JWT Token", "Bearer Token", "Basic Auth"));
        categories.put("☁️ 云服务密钥", Arrays.asList("阿里云AccessKey", "腾讯云SecretKey", "AWS AccessKey", "GitHub Token", "Google API Key"));
        categories.put("🗄️ 数据库配置", Arrays.asList("JDBC连接", "私钥", "密码配置"));
        categories.put("🤖 Webhook URLs", Arrays.asList("企业微信Webhook", "钉钉Webhook", "飞书Webhook", "Slack Webhook"));
        categories.put("📱 微信相关", Arrays.asList("微信AppID", "企业微信CorpID"));
        
        for (Map.Entry<String, java.util.List<String>> entry : categories.entrySet()) {
            outputArea.append(entry.getKey() + ":\n");
            for (String type : entry.getValue()) {
                outputArea.append("  • " + type + "\n");
            }
            outputArea.append("\n");
        }
        
        outputArea.append("使用方法:\n");
        outputArea.append("1. 输入目标URL\n");
        outputArea.append("2. 选择分析选项\n");
        outputArea.append("3. 点击'开始分析'按钮\n");
        outputArea.append("4. 查看检测结果\n\n");
        outputArea.append("提示: 勾选'实时监控'可自动分析Burp代理流量\n");
        outputArea.append(createSeparator("=", 50) + "\n\n");
    }
    
    /**
     * 创建分隔符字符串
     */
    private String createSeparator(String character, int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(character);
        }
        return sb.toString();
    }
    
    /**
     * 开始分析
     */
    private void startAnalysis() {
        String url = urlField.getText().trim();
        if (url.isEmpty()) {
            JOptionPane.showMessageDialog(mainPanel, "请输入目标URL", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }
        
        // URL格式检查和修正
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
            urlField.setText(url);
        }
        
        final String targetUrl = url;
        
        // 禁用按钮，开始分析
        analyzeButton.setEnabled(false);
        clearResults();
        updateStatus("正在分析: " + targetUrl);
        
        // 后台线程执行分析
        new Thread(() -> {
            try {
                performAnalysis(targetUrl);
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("❌ 分析失败: " + e.getMessage() + "\n");
                    updateStatus("分析失败");
                });
                callbacks.printError("Analysis failed: " + e.getMessage());
            } finally {
                SwingUtilities.invokeLater(() -> {
                    analyzeButton.setEnabled(true);
                    progressBar.setValue(0);
                    progressBar.setString("分析完成");
                });
            }
        }).start();
    }
    
    /**
     * 执行具体的分析逻辑
     */
    private void performAnalysis(String targetUrl) {
        try {
                         SwingUtilities.invokeLater(() -> {
                 appendToOutput("🚀 开始分析: " + targetUrl + "\n");
                 appendToOutput("时间: " + new Date() + "\n");
                 appendToOutput(createSeparator("-", 50) + "\n");
                 progressBar.setValue(10);
                 progressBar.setString("正在获取主页面...");
             });
            
            // 1. 获取主页面内容
            String mainPageContent = fetchPageContent(targetUrl);
            if (mainPageContent != null) {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("✅ 主页面获取成功 (" + mainPageContent.length() + " 字符)\n");
                    progressBar.setValue(30);
                    progressBar.setString("正在分析主页面...");
                });
                
                // 分析主页面
                analyzeContent(mainPageContent, "[主页面] " + targetUrl);
                
                // 2. 如果启用了JS分析，查找并分析JS文件
                if (autoAnalyzeJSCheckBox.isSelected()) {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(50);
                        progressBar.setString("正在查找JS文件...");
                    });
                    
                    Set<String> jsUrls = extractJavaScriptUrls(mainPageContent, targetUrl);
                    
                    SwingUtilities.invokeLater(() -> {
                        appendToOutput("🔍 发现 " + jsUrls.size() + " 个JavaScript文件\n");
                        progressBar.setValue(60);
                        progressBar.setString("正在分析JS文件...");
                    });
                    
                    // 分析JS文件
                    analyzeJavaScriptFiles(jsUrls);
                }
                
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(90);
                    progressBar.setString("正在生成报告...");
                });
                
                // 3. 生成分析报告
                generateReport();
                
            } else {
                SwingUtilities.invokeLater(() -> {
                    appendToOutput("❌ 无法获取页面内容\n");
                    updateStatus("获取页面失败");
                });
            }
            
        } catch (Exception e) {
            SwingUtilities.invokeLater(() -> {
                appendToOutput("❌ 分析过程中出错: " + e.getMessage() + "\n");
            });
            throw e;
        }
    }
    
    /**
     * 获取页面内容
     */
    private String fetchPageContent(String url) {
        try {
            URL targetUrl = new URL(url);
            IHttpService httpService = helpers.buildHttpService(
                targetUrl.getHost(), 
                getEffectivePort(targetUrl), 
                targetUrl.getProtocol()
            );
            
            byte[] request = helpers.buildHttpRequest(targetUrl);
            IHttpRequestResponse response = callbacks.makeHttpRequest(httpService, request);
            
            if (response.getResponse() != null) {
                return new String(response.getResponse());
            }
        } catch (Exception e) {
            callbacks.printError("Failed to fetch page content: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 分析内容中的敏感信息
     */
    private void analyzeContent(String content, String source) {
        if (content == null || content.isEmpty()) {
            return;
        }
        
        for (SensitivePattern sensitivePattern : SENSITIVE_PATTERNS) {
            Matcher matcher = sensitivePattern.pattern.matcher(content);
            java.util.List<String> findings = sensitiveData.get(sensitivePattern.name);
            
            while (matcher.find()) {
                String match = matcher.group();
                
                // 如果有捕获组，使用第一个捕获组
                if (matcher.groupCount() > 0 && matcher.group(1) != null) {
                    match = matcher.group(1);
                }
                
                // 清理和验证匹配结果
                String cleanMatch = cleanAndValidate(match, sensitivePattern.name);
                if (cleanMatch != null && !findings.contains(cleanMatch)) {
                    findings.add(cleanMatch);
                    sensitiveDataSources.put(cleanMatch, source);
                    
                    // 实时显示发现的敏感信息
                    SwingUtilities.invokeLater(() -> {
                        appendToOutput("🎯 发现 " + sensitivePattern.name + ": " + cleanMatch + "\n");
                        appendToOutput("   来源: " + source + "\n");
                    });
                }
            }
        }
    }
    
    /**
     * 清理和验证匹配结果
     */
    private String cleanAndValidate(String match, String type) {
        if (match == null || match.trim().isEmpty()) {
            return null;
        }
        
        match = match.trim();
        
        // 根据类型进行特殊处理
        switch (type) {
            case "邮箱地址":
                // 过滤示例邮箱
                if (match.contains("example.com") || match.contains("test.com") || 
                    match.contains("domain.com") || match.contains("localhost")) {
                    return null;
                }
                break;
                
            case "密码配置":
                // 过滤明显的示例密码
                String lower = match.toLowerCase();
                if (lower.contains("example") || lower.contains("test") || 
                    lower.contains("demo") || lower.equals("password") || 
                    lower.equals("123456") || lower.length() < 6) {
                    return null;
                }
                break;
                
            case "IPv4地址":
                // 过滤明显的示例IP
                if (match.equals("127.0.0.1") || match.equals("0.0.0.0") || 
                    match.equals("***************") || match.startsWith("127.0.0.")) {
                    return null;
                }
                break;
                
            case "JWT Token":
                // JWT至少要包含两个点
                if (!match.contains(".") || match.split("\\.").length < 3) {
                    return null;
                }
                break;
        }
        
        // 过滤太短的结果
        if (match.length() < 3) {
            return null;
        }
        
        return match;
    }
    
    /**
     * 提取JavaScript文件URLs
     */
    private Set<String> extractJavaScriptUrls(String htmlContent, String baseUrl) {
        Set<String> jsUrls = new HashSet<>();
        
        // 查找外部JS文件引用
        Pattern scriptPattern = Pattern.compile("<script[^>]+src=[\"']([^\"']+)[\"']", Pattern.CASE_INSENSITIVE);
        Matcher matcher = scriptPattern.matcher(htmlContent);
        
        while (matcher.find()) {
            String src = matcher.group(1);
            if (src.endsWith(".js") || src.contains(".js?")) {
                try {
                    if (src.startsWith("http")) {
                        jsUrls.add(src);
                    } else {
                        URL base = new URL(baseUrl);
                        URL jsUrl = new URL(base, src);
                        jsUrls.add(jsUrl.toString());
                    }
                } catch (Exception e) {
                    // 忽略无效URL
                }
            }
        }
        
        return jsUrls;
    }
    
    /**
     * 分析JavaScript文件
     */
    private void analyzeJavaScriptFiles(Set<String> jsUrls) {
        int count = 0;
        for (String jsUrl : jsUrls) {
            count++;
            final int currentCount = count;
            final int total = jsUrls.size();
            
            SwingUtilities.invokeLater(() -> {
                appendToOutput("📄 分析JS文件 (" + currentCount + "/" + total + "): " + jsUrl + "\n");
                progressBar.setValue(60 + (currentCount * 20 / total));
                progressBar.setString("分析JS文件 " + currentCount + "/" + total);
            });
            
            String jsContent = fetchPageContent(jsUrl);
            if (jsContent != null) {
                analyzeContent(jsContent, "[外部JS] " + jsUrl);
            }
            
            // 避免请求过快
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 生成分析报告
     */
         private void generateReport() {
         SwingUtilities.invokeLater(() -> {
             appendToOutput("\n" + createSeparator("=", 50) + "\n");
             appendToOutput("📊 分析报告\n");
             appendToOutput(createSeparator("=", 50) + "\n");
            
            int totalFindings = 0;
            for (SensitivePattern pattern : SENSITIVE_PATTERNS) {
                java.util.List<String> findings = sensitiveData.get(pattern.name);
                if (!findings.isEmpty()) {
                    appendToOutput("\n🔹 " + pattern.name + " (" + findings.size() + " 项):\n");
                    appendToOutput("   描述: " + pattern.description + "\n");
                    
                    for (String finding : findings) {
                        String source = sensitiveDataSources.get(finding);
                        appendToOutput("   • " + finding + "\n");
                        if (source != null) {
                            appendToOutput("     来源: " + source + "\n");
                        }
                    }
                    totalFindings += findings.size();
                }
            }
            
            if (totalFindings == 0) {
                appendToOutput("\n✅ 未发现敏感信息\n");
            } else {
                appendToOutput("\n⚠️  总计发现 " + totalFindings + " 项敏感信息\n");
                appendToOutput("建议: 请检查这些敏感信息是否应该暴露在前端代码中\n");
            }
            
                         appendToOutput("\n分析完成时间: " + new Date() + "\n");
             appendToOutput(createSeparator("=", 50) + "\n\n");
             
             updateStatus("分析完成 - 发现 " + totalFindings + " 项敏感信息");
         });
     }
    
    /**
     * 切换实时分析
     */
    private void toggleRealTimeAnalysis() {
        if (realTimeAnalysisCheckBox.isSelected()) {
            appendToOutput("🔄 实时监控已启用\n");
            updateStatus("实时监控中...");
        } else {
            appendToOutput("⏸️ 实时监控已停用\n");
            updateStatus("就绪");
        }
    }
    
    /**
     * 处理HTTP消息 - 用于实时分析
     */
    public void processHttpMessage(int toolFlag, boolean messageIsRequest, IHttpRequestResponse messageInfo) {
        if (!realTimeAnalysisCheckBox.isSelected() || messageIsRequest) {
            return;
        }
        
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(messageInfo);
            String url = requestInfo.getUrl().toString();
            
            // 避免重复分析同一个URL
            if (processedUrls.contains(url)) {
                return;
            }
            processedUrls.add(url);
            
            byte[] response = messageInfo.getResponse();
            if (response != null) {
                String responseString = new String(response);
                
                // 异步分析以避免阻塞
                new Thread(() -> {
                    analyzeContent(responseString, "[实时监控] " + url);
                }).start();
            }
        } catch (Exception e) {
            callbacks.printError("Real-time analysis error: " + e.getMessage());
        }
    }
    
    // 辅助方法
    private void clearResults() {
        outputArea.setText("");
        sensitiveData.clear();
        sensitiveDataSources.clear();
        processedUrls.clear();
        initializeData();
        showWelcomeMessage();
    }
    
    private void updateStatus(String status) {
        statusLabel.setText(status);
    }
    
    private void appendToOutput(String text) {
        outputArea.append(text);
        outputArea.setCaretPosition(outputArea.getDocument().getLength());
    }
    
    private void exportResults() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("导出敏感信息分析结果");
        
        if (fileChooser.showSaveDialog(mainPanel) == JFileChooser.APPROVE_OPTION) {
            try {
                String filename = fileChooser.getSelectedFile().getAbsolutePath();
                if (!filename.endsWith(".txt")) {
                    filename += ".txt";
                }
                
                try (PrintWriter writer = new PrintWriter(filename, "UTF-8")) {
                    writer.print(outputArea.getText());
                }
                
                JOptionPane.showMessageDialog(mainPanel, "结果已导出到: " + filename, 
                    "导出成功", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception e) {
                JOptionPane.showMessageDialog(mainPanel, "导出失败: " + e.getMessage(), 
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private void copyResults() {
        String text = outputArea.getText();
        if (!text.trim().isEmpty()) {
            StringSelection selection = new StringSelection(text);
            Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(selection, null);
            
            JOptionPane.showMessageDialog(mainPanel, "结果已复制到剪贴板", 
                "复制成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    private void saveConfiguration() {
        // 保存用户配置（URL历史、选项设置等）
        JOptionPane.showMessageDialog(mainPanel, "配置保存功能开发中", 
            "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    private int getEffectivePort(URL url) {
        int port = url.getPort();
        if (port == -1) {
            port = url.getProtocol().equals("https") ? 443 : 80;
        }
        return port;
    }
    
    // Getter方法
    public JPanel getPanel() {
        return mainPanel;
    }
    
    public String getTabCaption() {
        return "敏感信息提取";
    }
} 