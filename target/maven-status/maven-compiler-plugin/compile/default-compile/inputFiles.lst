D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\sessions\CookieJar.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\ProxyWebSocketHistoryFilter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\HostManagementTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\ByteArray.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyResponseToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\menu\BasicMenuItem.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\SecretKey.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\internal\ObjectFactoryLocator.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\internal\MontoyaObjectFactory.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\burpsuite\TaskExecutionEngine.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\extension\ExtensionWebSocketCreationStatus.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scope\ScopeChangeHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\HttpResponseEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\burpsuite\BurpSuite.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\extension\ExtensionWebSocketMessageHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\TimingData.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\settings\SettingsPanel.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\WebSocketMessageEditorProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\Audit.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\DigestAlgorithm.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\InteractionFilter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\Collaborator.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\HttpProtocol.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\DnsQueryType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\NumberUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\params\HttpParameterType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\ToolSource.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\hotkey\HotKeyContext.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\CompressionUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\params\HttpParameter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiScanMainTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadGeneratorProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadGenerator.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\issues\AuditIssueConfidence.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\HttpResponseReceived.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\ExtensionProvidedHttpRequestEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\Ai.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyResponseHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\WebSocketMessageEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\RequestToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\persistence\Persistence.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\BinaryMessageToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\sessions\ActionResult.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\URLEncoding.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\analysis\ResponseKeywordsAnalyzer.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\Direction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\ToolType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\ConsolidationAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\ReportFormat.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\HighlightColor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\Interaction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\TextMessageAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadProcessor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\MessageToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\HttpHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\analysis\Attribute.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\Proxy.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonStringNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\BurpExtender.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\EditorMode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\HttpMode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\issues\AuditIssueSeverity.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\StringUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ConfigurationTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\chat\Prompt.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Marker.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\ProxyWebSocketMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\EnhancedCapability.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\CollaboratorPayloadGenerator.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\MessageReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scope\Scope.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonException.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\HttpRequestEditorProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiDiscoveryTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\TextMessageToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\hotkey\HotKeyHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\HttpHeader.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\WebSocketCreated.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\HttpRequestResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadProcessingResult.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\RandomUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\persistence\Preferences.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\chat\PromptOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\sessions\SessionHandlingActionData.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\ScanTask.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\PathDiscoveryTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\MimeType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonArrayNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\InterceptedResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\WebSockets.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\Base64Utils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Range.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\IntruderInsertionPoint.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\issues\AuditIssueDefinition.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\UserInterface.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\PayloadOption.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\Utilities.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\InteractionType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadData.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\Theme.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\requests\HttpRequest.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiTestingTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\sitemap\SiteMapNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\MontoyaApi.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyRequestToBeSentAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\WebSocket.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\ComponentEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\BinaryMessageAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\requests\MalformedRequestException.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Annotations.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\sitemap\SiteMap.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\Scanner.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\InterceptedRequest.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\ScanConfiguration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\InvocationType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\HttpRequestTemplate.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\Selection.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyRequestHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\repeater\Repeater.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\SensitiveInfoTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\DnsDetails.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\extension\ExtensionWebSocketCreation.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\MessageEditorHttpRequestResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\Http.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\AuditIssueHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\InterceptedTextMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\TextMessageReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\WebSocketEditorEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiTestResultTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\TextMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\chat\PromptResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\AuditResult.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\HtmlEncoding.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\ExtensionProvidedHttpResponseEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\CompressionType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\RouteContent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\BurpSuiteEdition.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\comparer\Comparer.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\ProxyHttpRequestResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\JsRouteScanStyleTestResultTab.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\logger\LoggerHttpRequestResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\CrawlConfiguration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\BinaryMessageReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\ProxyWebSocket.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\HttpRequestTemplateGenerationOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\InteractionId.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\StatusCodeClass.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\PayloadProcessingAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\CryptoUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonParseException.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\requests\HttpTransformation.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\extension\ExtensionWebSocket.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyResponseReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\bchecks\BCheckImportResult.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\EnhancedSensitiveInfoExtractor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\HttpHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\params\ParsedHttpParameter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\ProxyWebSocketCreation.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\organizer\Organizer.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\extension\ExtensionUnloadingHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\InterceptedHttpMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\ScanCheck.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\Base64EncodingOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\logger\LoggerCaptureHttpRequestResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\RequestOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\HttpRequestEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\HttpDetails.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\ContentType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\sitemap\SiteMapFilter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\ExtensionProvidedWebSocketMessageEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\insertionpoint\AuditInsertionPoint.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\HttpResponse.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\RequestResponseSelection.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\GeneratedPayload.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\menu\Menu.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\ResponseAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\BurpExtension.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\persistence\PersistedList.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\issues\AuditIssue.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\chat\Message.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\URLUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonNumberNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\AuditConfiguration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ai\chat\PromptException.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\swing\SwingUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\CollaboratorServer.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\ExtensionProvidedEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\sessions\SessionHandlingAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\Crawl.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonNullNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\SmtpProtocol.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Task.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\RedirectionMode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scope\ScopeChange.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\WebSocketMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\RequestAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\EditorCreationContext.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\ApiSecurityCheckerMontoya.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\insertionpoint\AuditInsertionPointProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\HostContent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Version.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\audit\insertionpoint\AuditInsertionPointType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\HttpMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\MessageHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\ProxyMessageHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\ProxyWebSocketCreationHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\ResponseReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\ProxyHistoryFilter.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\persistence\PersistedObject.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\BinaryMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\AuditIssueContextMenuEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\menu\MenuBar.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\RawEditor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\CollaboratorPayload.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\bchecks\BChecks.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\decoder\Decoder.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\SmtpDetails.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\CrawlAndAudit.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\Editor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\collaborator\CollaboratorClient.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\AttackConfiguration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\SensitiveInfoExtractor.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\EditorOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\Cookie.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\intruder\Intruder.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\Base64DecodingOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\analysis\AttributeType.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\MessageAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\IBurpExtender.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\handler\HttpRequestToBeSent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\ByteUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\logging\Logging.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\project\Project.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonBooleanNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\HttpService.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\hotkey\HotKeyEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\bambda\BambdaImportResult.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\scanner\BuiltInAuditConfiguration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\json\JsonObjectNode.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\ContextMenuItemsProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\utilities\HtmlUtils.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\menu\MenuItem.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\bambda\Bambda.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\InvocationSource.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\extension\Extension.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\analysis\KeywordCount.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\editor\extension\HttpResponseEditorProvider.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\websocket\InterceptedBinaryMessage.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\core\Registration.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\proxy\http\ProxyRequestReceivedAction.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\websocket\WebSocketCreatedHandler.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\burpsuite\ShutdownOptions.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\ContextMenuEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\ui\contextmenu\WebSocketContextMenuEvent.java
D:\cursor\ChkApi_0x727-master\src\main\java\burp\api\montoya\http\message\responses\analysis\ResponseVariationsAnalyzer.java
