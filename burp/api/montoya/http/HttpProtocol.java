/*
 * Copyright (c) 2022-2023. PortSwigger Ltd. All rights reserved.
 *
 * This code may be used to extend the functionality of Burp Suite Community Edition
 * and Burp Suite Professional, provided that this usage does not violate the
 * license terms for those products.
 */

package burp.api.montoya.http;

/**
 * HTTP protocols.
 */
public enum HttpProtocol
{
    /**
     * Hypertext Transfer Protocol
     */
    HTTP,
    /**
     * Hypertext Transfer Protocol Secure
     */
    HTTPS
}
